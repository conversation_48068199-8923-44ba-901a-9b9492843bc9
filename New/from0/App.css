/* responsive.css - Add this file to your project and import it in your main component */

/* ===== Basic Reset and Global Styles ===== */
:root {
  --safe-area-inset-top: env(safe-area-inset-top, 0px);
  --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
  --browser-ui-compensation-top: 0px;
  --browser-ui-compensation-bottom: 0px;
  /* Dynamic viewport height for Safari */
  --vh: 1vh;
}

/* Update viewport height variable on resize */
@supports (height: 100dvh) {
  :root {
    --vh: 1dvh;
  }
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  /* Use dynamic viewport height instead of vh - commented out to fix container display */
  /* min-height: calc(100 * var(--vh)); */
  /* Add padding to compensate for browser UI elements */
  padding-top: var(--browser-ui-compensation-top);
  padding-bottom: var(--browser-ui-compensation-bottom);
  /* Ensure smooth transitions when browser UI appears/disappears */
  transition: padding-top 0.3s ease, padding-bottom 0.3s ease;
}

/* ===== Modal Styles ===== */
.modal-backdrop {
  /* Prevent scrolling backdrop layering issues */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  /* Ensure backdrop doesn't create additional layers when scrolling */
  background-attachment: fixed;
  /* Prevent backdrop from being affected by scroll */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  /* Ensure backdrop stays in place */
  will-change: auto;
}

/* ===== Sidebar Styles ===== */
.sidebar-container {
  height: 100vh; /* Fallback */
  /* Commented out to fix container display */
  /* height: calc(100 * var(--vh)); */ /* Dynamic viewport height */
  position: fixed;
  left: 0;
  top: 0;
  z-index: 50 !important; /* Increase z-index to ensure sidebar is always on top */
  transition: transform 0.3s ease, width 0.3s ease;
  overflow-y: auto; /* Allow scrolling within sidebar */
  pointer-events: auto !important; /* Ensure pointer events work */
}

.sidebar-expanded {
  width: 250px !important; /* Force 250px width when expanded */
}
.flex-1, .main-content {
  margin-left: 0px !important; /* When sidebar is expanded */
}
.sidebar-collapsed {
  width: 64px !important; /* Width when collapsed */
}

/* Mobile sidebar (hidden by default on small screens) */
@media (max-width: 800px) {
  .sidebar-container {
    transform: translateX(-100%); /* Hide off-screen */
  }

  /* Only show when explicitly toggled on mobile */
  .sidebar-container.sidebar-mobile-active {
    transform: translateX(0); /* Show when toggled */
  }


  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 30;
  }


}

/* ===== Main Content Styles ===== */
.flex-1, .main-content {
  transition: margin-left 0.3s ease;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10;
  /* Removed min-height that was breaking container display */
  /* min-height: calc(100 * var(--vh)); */
  /* Add padding for iOS browser UI elements */
  padding-top: max(env(safe-area-inset-top), var(--browser-ui-compensation-top));
  padding-bottom: max(env(safe-area-inset-bottom), var(--browser-ui-compensation-bottom));
}

@media (min-width: 801px) {
  .flex-1, .main-content {
    margin-left: 250px !important; /* When sidebar is expanded */
  }

  /* When sidebar is collapsed */
  .sidebar-collapsed ~ .flex-1,
  .sidebar-collapsed ~ .main-content,
  .sidebar-collapsed ~ div .flex-1,
  .sidebar-collapsed ~ div .main-content {
    margin-left: 64px !important;
  }
}

/* ===== Top Menu Styles ===== */
.top-menu {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
  height: 64px; /* Fixed height to prevent resizing */
  min-height: 64px; /* Ensure minimum height */
  max-height: 64px; /* Ensure maximum height */
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 30;
  will-change: transform; /* Optimize for animations */
}

/* Search results dropdown styles */
.search-container {
  position: relative;
  border: none !important;
  background: transparent !important;
}

.search-container:before,
.search-container:after {
  display: none !important;
  content: none !important;
}

/* Fix for search input in TopMenu */
.top-menu .search-container input {
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  background-color: white;
  box-shadow: none;
  position: relative;
  z-index: 0;
}

/* Remove any unwanted lines or borders */
.top-menu .search-container input::before,
.top-menu .search-container input::after {
  display: none !important;
  content: none !important;
}

.search-container .absolute {
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

@media (max-width: 800px) {
  .search-container {
    width: 100%;
    max-width: calc(100% - 40px); /* Account for menu button */
  }

  /* Search results dropdown on mobile */
  .search-container .absolute {
    width: calc(100vw - 2rem) !important;
    left: 0;
    max-height: 60vh;
  }

  /* Make search results text smaller on mobile */
  .search-container .absolute .text-sm {
    font-size: 0.75rem !important;
  }

  .search-container .absolute .text-xs {
    font-size: 0.65rem !important;
  }

  /* Make top menu smaller on mobile */
  .top-menu {
    height: 50px !important;
    min-height: 50px !important;
    max-height: 50px !important;
    padding: 0.5rem 0.75rem !important;
  }

  /* Make top menu icons smaller */
  .top-menu svg {
    width: 16px !important;
    height: 16px !important;
  }

  /* Make search input smaller */
  .top-menu input {
    height: 32px !important;
    font-size: 0.8rem !important;
  }

  /* Make user profile icon smaller */
  .top-menu .w-8.h-8 {
    width: 28px !important;
    height: 28px !important;
  }

  /* Make buttons smaller */
  button {
    padding: 0.35rem 0.65rem !important;
    font-size: 0.8rem !important;
  }

  /* Make inputs smaller */
  input, select, textarea {
    padding: 0.35rem 0.65rem !important;
    font-size: 0.8rem !important;
    height: auto !important;
  }

  /* Fix for search inputs with icons */
  input[type="text"][placeholder*="Search"] {
    padding-left: 1.75rem !important;
    border-width: 1px !important; /* Ensure border is not too thick */
  }

  /* Fix for select dropdowns to prevent text overlap */
  select {
    padding-right: 1.75rem !important;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  /* Fix for dropdown arrows */
  select + svg {
    right: 0.5rem !important;
  }

  /* Make headings smaller */
  h1 {
    font-size: 1.5rem !important;
  }

  h2 {
    font-size: 1.25rem !important;
  }

  h3 {
    font-size: 1.1rem !important;
  }

  /* Make paragraph text smaller */
  p, span, div {
    font-size: 0.85rem !important;
  }

  /* Make labels smaller */
  label {
    font-size: 0.8rem !important;
  }
}

/* ===== Responsive Table Styles ===== */
.table {
  table-layout: fixed;
  width: 100%;
}

/* Responsive tables */
@media (max-width: 1124px) {
  .table th,
  .table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.875rem;
  }

  /* Hide less important columns at smaller sizes */
  .hide-sm {
    display: none;
  }
}

@media (max-width: 800px) {
  .hide-xs {
    display: none;
  }

  .table th,
  .table td {
    padding: 0.5rem 0.2rem !important;
    font-size: 0.7rem !important;
  }

  /* Make sure data doesn't overflow */
  .table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Description cell should wrap */
  .table td:nth-child(2) {
    white-space: normal;
    word-break: break-word;
  }

  /* Make table rows more compact */
  .table tr {
    height: auto !important;
    min-height: 30px !important;
  }

  /* Make table buttons smaller */
  .table button {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.7rem !important;
  }

  /* Make table icons smaller */
  .table svg {
    width: 14px !important;
    height: 14px !important;
  }
}

/* ===== Metric Cards ===== */
.metric-card {
  transition: transform 0.2s, box-shadow 0.2s;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Responsive grid for metric cards */
@media (max-width: 800px) {
  .metric-card {
    margin-bottom: 0.75rem !important;
    padding: 0.75rem !important;
  }

  /* Make metric card text smaller */
  .metric-card .text-sm {
    font-size: 0.7rem !important;
  }

  .metric-card .text-2xl {
    font-size: 1.25rem !important;
  }

  /* Make metric card icons smaller */
  .metric-card svg {
    width: 30px !important;
    height: 30px !important;
  }
}

/* ===== Icon Shadows ===== */
.icon-dropshadow-success {
  filter: drop-shadow(0 4px 6px rgba(16, 185, 129, 0.3));
}

.icon-dropshadow-warning {
  filter: drop-shadow(0 4px 6px rgba(245, 158, 11, 0.3));
}

.icon-dropshadow-info {
  filter: drop-shadow(0 4px 6px rgba(59, 130, 246, 0.3));
}

.icon-dropshadow-danger {
  filter: drop-shadow(0 4px 6px rgba(239, 68, 68, 0.3));
}

/* ===== Sidebar Item Styles ===== */
.sidebar-item {
  transition: background-color 0.2s, color 0.2s;
}

.sidebar-item:hover {
  background-color: #f3f4f6;
  color: #4f46e5;
}

.sidebar-item.active {
  background-color: #eef2ff;
  color: #4f46e5;
  border-left: 3px solid #4f46e5;
}

/* ===== Profile Image ===== */
.profile-image {
  border: 3px solid #e5e7eb;
  transition: transform 0.2s;
}

.profile-image:hover {
  transform: scale(1.05);
}

/* ===== Task Row Styles ===== */
.task-row {
  cursor: pointer;
  transition: background-color 0.2s;
}

.task-row:hover {
  background-color: #f9fafb;
}

/* ===== Login Page Responsiveness ===== */
@media (max-width: 800px) {
  /* Login page specific styles */
  .w-full.md\:w-1\/2.bg-white.p-8 {
    padding: 1rem !important;
  }

  /* Login form inputs */
  .w-full.md\:w-1\/2.bg-white.p-8 input[type="email"],
  .w-full.md\:w-1\/2.bg-white.p-8 input[type="password"] {
    height: 36px !important;
    font-size: 0.8rem !important;
    padding-left: 36px !important;
  }

  /* Login form icons */
  .w-full.md\:w-1\/2.bg-white.p-8 .absolute.inset-y-0.left-0.flex.items-center.pl-3 svg {
    width: 18px !important;
    height: 18px !important;
    margin-left: 8px !important;
  }

  /* Login button */
  .w-full.md\:w-1\/2.bg-white.p-8 button[type="submit"] {
    height: 40px !important;
    font-size: 0.85rem !important;
  }
}

/* ===== Modal Responsiveness ===== */
/* General modal styles for all screen sizes */
.fixed.inset-0.flex.items-center.justify-center {
  /* Use dynamic viewport height */
  height: 100vh;
  /* Commented out to fix container display */
  /* height: calc(100 * var(--vh)); */
  /* Ensure modals are above other content - use inherit to respect individual z-index values */
  z-index: inherit;
  /* Allow scrolling if modal is taller than viewport */
  overflow-y: auto;
  /* Add padding for iOS browser UI elements */
  padding-top: max(env(safe-area-inset-top), var(--browser-ui-compensation-top));
  padding-bottom: max(env(safe-area-inset-bottom), var(--browser-ui-compensation-bottom));
}

/* Modal container styles */
.fixed.inset-0.flex.items-center.justify-center > div {
  /* Prevent modal from being cut off */
  position: relative;
  margin: auto;
  /* Add some space at the top and bottom */
  margin-top: 2vh;
  margin-bottom: 2vh;
}

@media (max-width: 640px) {
  /* Modal container on small screens */
  .fixed.inset-0.flex.items-center.justify-center {
    align-items: flex-start; /* Align to top instead of center */
    padding-top: 10px; /* Add some space at the top */
    padding-bottom: 10px; /* Add some space at the bottom */
  }

  .fixed.inset-0.flex.items-center.justify-center > div {
    width: 98%;
    /* Commented out to fix container display */
    /* max-height: calc(100 * var(--vh) - 20px); */ /* Use dynamic viewport height minus padding */
    max-height: 90vh; /* Use viewport height instead */
    font-size: 0.85rem !important; /* Increased from 0.65rem */
    margin-top: 0; /* Remove top margin on mobile */
    margin-bottom: 0; /* Remove bottom margin on mobile */
    transform: scale(0.9); /* Increased from 0.75 (less reduction) */
    transform-origin: top center; /* Scale from the top center */
  }

  /* Ensure modal content is scrollable */
  .fixed.inset-0.flex.items-center.justify-center > div > div {
    /* Commented out to fix container display */
    /* max-height: calc(100 * var(--vh) - 20px); */
    max-height: 90vh; /* Use viewport height instead */
    overflow-y: auto;
  }

  /* Make modal headers appropriately sized */
  .modal-header, .modal-title, .modal h1, .modal h2, .modal h3,
  .fixed.inset-0.flex.items-center.justify-center h1,
  .fixed.inset-0.flex.items-center.justify-center h2,
  .fixed.inset-0.flex.items-center.justify-center h3,
  .fixed.inset-0.flex.items-center.justify-center .text-xl,
  .fixed.inset-0.flex.items-center.justify-center .text-lg {
    font-size: 1rem !important; /* Increased from 0.7rem */
    padding: 0.6rem !important; /* Increased from 0.45rem */
  }

  /* Make modal content appropriately sized */
  .modal-body, .modal-content,
  .fixed.inset-0.flex.items-center.justify-center .p-3,
  .fixed.inset-0.flex.items-center.justify-center .p-4,
  .fixed.inset-0.flex.items-center.justify-center .p-6 {
    padding: 0.6rem !important; /* Increased from 0.45rem */
  }

  /* Make modal footer appropriately sized */
  .modal-footer,
  .fixed.inset-0.flex.items-center.justify-center .border-t {
    padding: 0.5rem 0.6rem !important; /* Increased from 0.3rem/0.45rem */
  }

  /* Make modal text appropriately sized */
  .fixed.inset-0.flex.items-center.justify-center p,
  .fixed.inset-0.flex.items-center.justify-center span,
  .fixed.inset-0.flex.items-center.justify-center div,
  .fixed.inset-0.flex.items-center.justify-center label {
    font-size: 0.85rem !important; /* Increased from 0.55rem */
  }

  /* Make modal form elements appropriately sized */
  .modal input, .modal select, .modal textarea,
  .fixed.inset-0.flex.items-center.justify-center input,
  .fixed.inset-0.flex.items-center.justify-center select,
  .fixed.inset-0.flex.items-center.justify-center textarea {
    font-size: 0.85rem !important; /* Increased from 0.55rem */
    padding: 0.4rem 0.5rem !important; /* Increased from 0.225rem/0.3rem */
    height: auto !important;
  }

  /* Make modal buttons appropriately sized */
  .modal button,
  .fixed.inset-0.flex.items-center.justify-center button {
    padding: 0.4rem 0.6rem !important; /* Increased from 0.225rem/0.375rem */
    font-size: 0.85rem !important; /* Increased from 0.55rem */
  }

  /* Make modal tabs appropriately sized */
  .modal-tabs button, .modal .tab,
  .fixed.inset-0.flex.items-center.justify-center .tab {
    padding: 0.4rem 0.6rem !important; /* Increased from 0.225rem/0.375rem */
    font-size: 0.85rem !important; /* Increased from 0.55rem */
  }

  /* Fix for modals with overflow content */
  .max-h-\[90vh\], .max-h-\[85vh\] {
    max-height: calc(100 * var(--vh) - 20px) !important;
  }

  /* Ensure modal content is scrollable */
  .overflow-y-auto {
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  }
}

/* ===== Custom Animation ===== */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.sidebar-overlay {
  animation: fadeIn 0.3s ease;
}
  /* Set column widths */
  .table th:nth-child(1),
  .table td:nth-child(1) {
    width: 60px; /* ID column - narrower width */
  }

  .table th:nth-child(2),
  .table td:nth-child(2) {
    width: 40%; /* Description column */
  }
  .table th:nth-child(3),
  .table td:nth-child(3) {
    width: 20%; /* Description column */
  }

  .table th:nth-child(4),
  .table td:nth-child(4),
  .table th:nth-child(5),
  .table td:nth-child(5),
  .table th:nth-child(6),
  .table td:nth-child(6),
  .table th:nth-child(7),
  .table td:nth-child(7),
  .table th:nth-child(8),
  .table td:nth-child(8) {
    width: 120px; /* Priority and Status columns */
  }

  .table th:last-child,
  .table td:last-child {
    width: 110px; /* Actions column */
  }





  /* These are the critical CSS rules you need to fix */

/* Mobile sidebar styles - update these specific rules */
@media (max-width: 800px) {
  /* Active state - visible */
  .sidebar-mobile-active {
    transform: translateX(0) !important;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
  }

  /* Overlay */
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 30; /* Just below sidebar */
  }
}

/* Critical fix for sidebar visibility */
@media (max-width: 800px) {
  /* Base state - sidebar is hidden by default on mobile */
  .sidebar-container {
    transform: translateX(-100%) !important;
    position: fixed !important;
    width: 250px !important;
    z-index: 40 !important;
    transition: transform 0.3s ease !important;
    height: 100% !important;
  }

  /* When the sidebar-mobile-active class is applied, show the sidebar */
  .sidebar-container.sidebar-mobile-active {
    transform: translateX(0) !important;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3) !important;
  }

  /* Ensure main content is not covered by sidebar */
  .flex-1, .main-content {
    margin-left: 0 !important;
    width: 100% !important;
  }
}

/* Ensure overlay is properly styled and positioned */
.sidebar-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  z-index: 35 !important; /* Below sidebar but above content */
  display: block !important;
}


/* Add this to your App.css file */

/* Ensure menu text is visible when sidebar is shown on mobile */
@media (max-width: 800px) {
  .sidebar-container.sidebar-mobile-active .menu-text,
  .sidebar-container.sidebar-mobile-active .help-text {
    display: inline-block !important;
    font-size: 0.85rem !important; /* Smaller font size for menu text */
  }

  /* Fix for profile container */
  .sidebar-container.sidebar-mobile-active .profile-container {
    display: flex !important;
    padding: 0.75rem !important; /* Reduced padding */
  }

  /* Fix for collapsed profile to not show when mobile sidebar is active */
  .sidebar-container.sidebar-mobile-active .collapsed-profile {
    display: none !important;
  }

  /* Make sidebar items smaller on mobile */
  .sidebar-item {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.85rem !important;
  }

  /* Make sidebar icons smaller */
  .sidebar-item svg {
    width: 16px !important;
    height: 16px !important;
  }

  /* Make sidebar width smaller on mobile when active */
  .sidebar-container.sidebar-mobile-active {
    width: 220px !important;
  }

  /* Fix for search icons in input fields */
  .search-icon-position {
    left: 0.5rem !important;
  }

  /* Fix for search icons in all components */
  input[type="text"][placeholder*="Search"] ~ svg,
  .relative svg.absolute.left-2,
  .relative svg.absolute.left-2\.5,
  .relative svg.absolute.left-3 {
    left: 0.5rem !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
  }

  /* Fix specifically for Lucide icons in search bar */
  .search-container svg.lucide-search {
    width: 16px !important;
    height: 16px !important;
    stroke-width: 2px !important;
    position: absolute !important;
    left: 8px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 1 !important;
  }

  /* Fix for search container in TopMenu */
  .top-menu .search-container {
    border: none !important;
    background: transparent !important;
  }

  /* Fix for search input in TopMenu */
  .top-menu .search-container input {
    border: 1px solid #e5e7eb !important;
    border-radius: 0.375rem !important;
    background-color: white !important;
    box-shadow: none !important;
    height: 32px !important;
  }

  /* Remove any unwanted lines or borders */
  .top-menu .search-container:before,
  .top-menu .search-container:after,
  .top-menu .search-container input:before,
  .top-menu .search-container input:after {
    display: none !important;
    content: none !important;
  }

  /* Fix for login page icons overlapping text */
  .relative .absolute.inset-y-0.left-0.flex.items-center.pl-3 {
    left: 0 !important;
    width: 30px !important;
    display: flex !important;
    justify-content: center !important;
  }

  .relative input[type="email"],
  .relative input[type="password"],
  .relative input[type="text"] {
    padding-left: 30px !important;
  }
}
/* Add these rules to your App.css file to fix the collapsed sidebar in desktop/tablet view */

/* Ensure proper sidebar width in collapsed state */
@media (min-width: 801px) {
  .sidebar-collapsed {
    width: 64px !important;
    min-width: 64px !important;
    max-width: 64px !important;
  }

  .sidebar-expanded {
    width: 250px !important;
    min-width: 250px !important;
  }

  /* Ensure main content properly adjusts to sidebar width */
  .sidebar-collapsed ~ .flex-1,
  .sidebar-collapsed ~ .main-content,
  .sidebar-collapsed ~ div .flex-1,
  .sidebar-collapsed ~ div .main-content {
    margin-left: 64px !important;
    width: calc(100% - 64px) !important;
  }

  .sidebar-expanded ~ .flex-1,
  .sidebar-expanded ~ .main-content,
  .sidebar-expanded ~ div .flex-1,
  .sidebar-expanded ~ div .main-content {
    margin-left: 250px !important;
    width: calc(100% - 250px) !important;
  }
}

/* Fix logo alignment and visibility in collapsed state */
.sidebar-collapsed .logo-container {
  justify-content: center !important;
  padding: 1rem 0 !important;
}

.sidebar-collapsed .logo-text {
  display: none !important;
}

/* Improve transition between states */
.sidebar-container {
  transition: width 0.3s ease !important;
}

.flex-1, .main-content {
  transition: margin-left 0.3s ease, width 0.3s ease !important;
}

/* Fix for ZetServers logo in header */
.top-menu {
  position: relative;
  z-index: 20;
}

/* Fix for dashboard content */
.dashboard-content {
  position: relative;
  z-index: 10;
}
/* Add these rules to your App.css file to fix the profile container in collapsed state */

/* Profile container styling in collapsed sidebar */
.sidebar-collapsed .profile-container {
  padding: 3.4rem 0 !important;

  align-items: center !important;
  justify-content: center !important;
}

/* Global mobile spacing adjustments */
@media (max-width: 800px) {
  /* Reduce all paddings */
  .p-1, .p-2, .p-3, .p-4, .p-5, .p-6, .p-8, .p-10, .p-12 {
    padding: 0.5rem !important;
  }

  .px-1, .px-2, .px-3, .px-4, .px-5, .px-6, .px-8, .px-10, .px-12 {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  .py-1, .py-2, .py-3, .py-4, .py-5, .py-6, .py-8, .py-10, .py-12 {
    padding-top: 0.35rem !important;
    padding-bottom: 0.35rem !important;
  }

  /* Reduce all margins */
  .m-1, .m-2, .m-3, .m-4, .m-5, .m-6, .m-8, .m-10, .m-12 {
    margin: 0.5rem !important;
  }

  .mx-1, .mx-2, .mx-3, .mx-4, .mx-5, .mx-6, .mx-8, .mx-10, .mx-12 {
    margin-left: 0.5rem !important;
    margin-right: 0.5rem !important;
  }

  .my-1, .my-2, .my-3, .my-4, .my-5, .my-6, .my-8, .my-10, .my-12 {
    margin-top: 0.35rem !important;
    margin-bottom: 0.35rem !important;
  }

  /* Reduce gap in flex and grid layouts */
  .gap-1, .gap-2, .gap-3, .gap-4, .gap-5, .gap-6, .gap-8, .gap-10, .gap-12 {
    gap: 0.35rem !important;
  }

  /* Make all containers more compact */
  .container {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }
}

/* Utility class for text overflow */
.text-ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

/* Page transition animations */
.page-transition {
  animation: fadeIn 0.15s ease-out;
  will-change: opacity;
  width: 100%;
  /* Removed min-height that was breaking container display */
  /* min-height: calc(100 * var(--vh)); */
  /* Add padding for iOS browser UI elements */
  padding-top: max(env(safe-area-inset-top), var(--browser-ui-compensation-top));
  padding-bottom: max(env(safe-area-inset-bottom), var(--browser-ui-compensation-bottom));
  /* Ensure smooth transitions */
  transition: padding-top 0.3s ease, padding-bottom 0.3s ease, opacity 0.15s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0.9;
  }
  to {
    opacity: 1;
  }
}

.sidebar-collapsed .profile-container .profile-image {
  width: 40px !important;
  height: 40px !important;
  margin-bottom: 0 !important;
}

.sidebar-collapsed .profile-container .profile-text {
  display: none !important;

}



.sidebar-expanded .profile-container .profile-image,
.sidebar-mobile-active .profile-container .profile-image {
  width: 74px !important;
  height: 76px !important;

}

.sidebar-collapsed .profile-container .profile-image{
  width: 40px !important;
  height: 40px !important;

}



@media (max-width: 800px) {
  .sidebar-collapsed .profile-container .profile-image{
    width: 76px !important;
    height: 76px !important;

  }
}

/* ===== Modal Component Specific Fixes ===== */
/* These styles target specific modal components to ensure they display correctly on mobile */

/* Fix for all modal components */
@media (max-width: 640px) {
  /* ServerDetailsModal */
  .max-w-6xl.flex.flex-col.max-h-\[90vh\] {
    /* Commented out to fix container display */
    /* max-height: calc(100 * var(--vh) - 20px) !important; */
    max-height: 90vh !important;
    margin: 10px auto !important;
  }

  /* ServiceDetailModal */
  .max-w-3xl.w-full.max-h-\[90vh\] {
    /* Commented out to fix container display */
    /* max-height: calc(100 * var(--vh) - 20px) !important; */
    max-height: 90vh !important;
    margin: 10px auto !important;
  }

  /* InvoiceModal */
  .max-w-4xl.w-full.max-h-\[90vh\] {
    /* Commented out to fix container display */
    /* max-height: calc(100 * var(--vh) - 20px) !important; */
    max-height: 90vh !important;
    margin: 10px auto !important;
  }

  /* SelectSubnetModal */
  .max-w-2xl.w-full.max-h-\[85vh\] {
    /* Commented out to fix container display */
    /* max-height: calc(100 * var(--vh) - 20px) !important; */
    max-height: 85vh !important;
    margin: 10px auto !important;
  }

  /* PortAssignmentModal */
  .max-w-4xl.mx-4.flex.flex-col.max-h-\[90vh\] {
    /* Commented out to fix container display */
    /* max-height: calc(100 * var(--vh) - 20px) !important; */
    max-height: 90vh !important;
    margin: 10px auto !important;
    width: 95% !important;
  }

  /* NewOrderModal */
  .max-w-\[1050px\].overflow-hidden {
    /* Commented out to fix container display */
    /* max-height: calc(100 * var(--vh) - 20px) !important; */
    max-height: 90vh !important;
    margin: 10px auto !important;
    overflow-y: auto !important;
  }

  /* Fix for modal content scrolling */
  .max-h-\[75vh\].overflow-y-auto,
  .max-h-\[calc\(85vh-120px\)\].overflow-y-auto,
  .overflow-y-auto.max-h-\[calc\(90vh-120px\)\] {
    /* Commented out to fix container display */
    /* max-height: calc(100 * var(--vh) - 120px) !important; */
    max-height: calc(90vh - 120px) !important;
    -webkit-overflow-scrolling: touch;
  }

  /* Fix for sticky headers in modals */
  .sticky.top-0.z-10 {
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
    background-color: white !important;
  }

  /* Fix for modal footer positioning */
  .border-t.flex.justify-end {
    position: sticky !important;
    bottom: 0 !important;
    background-color: white !important;
    z-index: 10 !important;
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  /* Component-specific styles for modals */
  /* ServerDetailsModal */
  .max-w-6xl.flex.flex-col.max-h-\[90vh\] p,
  .max-w-6xl.flex.flex-col.max-h-\[90vh\] span,
  .max-w-6xl.flex.flex-col.max-h-\[90vh\] div,
  .max-w-6xl.flex.flex-col.max-h-\[90vh\] label {
    font-size: 0.85rem !important;
  }

  .max-w-6xl.flex.flex-col.max-h-\[90vh\] h2,
  .max-w-6xl.flex.flex-col.max-h-\[90vh\] h3,
  .max-w-6xl.flex.flex-col.max-h-\[90vh\] .text-lg,
  .max-w-6xl.flex.flex-col.max-h-\[90vh\] .text-xl {
    font-size: 1rem !important;
  }

  /* ServiceDetailModal */
  .max-w-3xl.w-full.max-h-\[90vh\] p,
  .max-w-3xl.w-full.max-h-\[90vh\] span,
  .max-w-3xl.w-full.max-h-\[90vh\] div,
  .max-w-3xl.w-full.max-h-\[90vh\] label {
    font-size: 0.85rem !important;
  }

  /* InvoiceModal */
  .max-w-4xl.w-full.max-h-\[90vh\] p,
  .max-w-4xl.w-full.max-h-\[90vh\] span,
  .max-w-4xl.w-full.max-h-\[90vh\] div,
  .max-w-4xl.w-full.max-h-\[90vh\] label {
    font-size: 0.85rem !important;
  }

  /* SelectSubnetModal */
  .max-w-2xl.w-full.max-h-\[85vh\] p,
  .max-w-2xl.w-full.max-h-\[85vh\] span,
  .max-w-2xl.w-full.max-h-\[85vh\] div,
  .max-w-2xl.w-full.max-h-\[85vh\] label {
    font-size: 0.85rem !important;
  }

  /* PortAssignmentModal */
  .max-w-4xl.mx-4.flex.flex-col.max-h-\[90vh\] p,
  .max-w-4xl.mx-4.flex.flex-col.max-h-\[90vh\] span,
  .max-w-4xl.mx-4.flex.flex-col.max-h-\[90vh\] div,
  .max-w-4xl.mx-4.flex.flex-col.max-h-\[90vh\] label {
    font-size: 0.85rem !important;
  }

  /* NewOrderModal */
  .max-w-\[1050px\].overflow-hidden p,
  .max-w-\[1050px\].overflow-hidden span,
  .max-w-\[1050px\].overflow-hidden div,
  .max-w-\[1050px\].overflow-hidden label {
    font-size: 0.85rem !important;
  }

  /* Make all modal icons appropriately sized */
  .fixed.inset-0.flex.items-center.justify-center svg {
    width: 16px !important;
    height: 16px !important;
  }

  /* Make all modal form elements appropriately sized */
  .fixed.inset-0.flex.items-center.justify-center input,
  .fixed.inset-0.flex.items-center.justify-center select,
  .fixed.inset-0.flex.items-center.justify-center textarea {
    font-size: 0.85rem !important;
    padding: 0.4rem 0.5rem !important;
    height: 32px !important;
  }

  /* Make all modal buttons appropriately sized */
  .fixed.inset-0.flex.items-center.justify-center button {
    padding: 0.4rem 0.6rem !important;
    font-size: 0.85rem !important;
    height: auto !important;
    min-height: 32px !important;
  }

  /* Specific fix for ticket modal buttons */
  .fixed.inset-0.flex.items-center.justify-center button.px-2.py-1 {
    padding: 0.4rem 0.6rem !important;
    font-size: 0.85rem !important;
    height: auto !important;
    min-height: 32px !important;
  }

  /* Specific fixes for the ticket modal shown in the screenshot */
  /* Ticket modal container */
  .fixed.inset-0.flex.items-center.justify-center > div.bg-white {
    transform: scale(1) !important; /* Override the scale reduction */
    width: 100% !important;
    max-width: 100% !important;
    height: auto !important;
    /* Commented out to fix container display */
    /* max-height: calc(100 * var(--vh) - 10px) !important; */
    max-height: 95vh !important;
    margin: 5px !important;
    border-radius: 8px !important;
  }

  /* Ticket modal header */
  .fixed.inset-0.flex.items-center.justify-center > div.bg-white > div:first-child {
    padding: 0.75rem !important;
    font-size: 1rem !important;
  }

  /* Ticket modal content */
  .fixed.inset-0.flex.items-center.justify-center > div.bg-white > div:not(:first-child) {
    font-size: 0.9rem !important;
    padding: 0.5rem !important;
  }

  /* Ticket modal buttons */
  .fixed.inset-0.flex.items-center.justify-center > div.bg-white button {
    font-size: 0.9rem !important;
    padding: 0.5rem 0.75rem !important;
    height: auto !important;
    min-height: 36px !important;
  }

  /* Phone view circular close button */
  .fixed.inset-0.flex.items-center.justify-center > div.bg-white button[aria-label="Close"],
  .fixed.inset-0.flex.items-center.justify-center > div.bg-white .close-button {
    position: absolute !important;
    top: 0.75rem !important;
    right: 0.75rem !important;
    width: 32px !important;
    height: 32px !important;
    min-height: 32px !important;
    border-radius: 50% !important;
    background-color: #f3f4f6 !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 60 !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    border: none !important;
  }

  .fixed.inset-0.flex.items-center.justify-center > div.bg-white button[aria-label="Close"] svg,
  .fixed.inset-0.flex.items-center.justify-center > div.bg-white .close-button svg {
    width: 20px !important;
    height: 20px !important;
  }

  /* Make response textarea bigger on phone view */
  .fixed.inset-0.flex.items-center.justify-center > div.bg-white .response-textarea {
    min-height: 180px !important;
    height: auto !important;
    font-size: 16px !important; /* Native font size to prevent zoom on iOS */
    padding: 12px !important;
    margin-bottom: 16px !important;
    border-width: 2px !important;
    border-color: #d1d5db !important;
    border-radius: 8px !important;
  }

  /* Improve response actions layout on mobile */
  .fixed.inset-0.flex.items-center.justify-center > div.bg-white .response-actions {
    display: flex !important;
    width: 100% !important;
    margin-top: 8px !important;
  }

  .fixed.inset-0.flex.items-center.justify-center > div.bg-white .response-actions button,
  .fixed.inset-0.flex.items-center.justify-center > div.bg-white .response-actions label {
    height: 48px !important;
    min-height: 48px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 14px !important;
    padding: 0 16px !important;
  }

  .fixed.inset-0.flex.items-center.justify-center > div.bg-white .response-actions button {
    flex-grow: 1 !important;
  }

  /* Make checkbox bigger and more tappable */
  .fixed.inset-0.flex.items-center.justify-center > div.bg-white input[type="checkbox"] {
    width: 18px !important;
    height: 18px !important;
    margin-right: 8px !important;
  }

  /* Improve ticket details layout on phone */
  .fixed.inset-0.flex.items-center.justify-center > div.bg-white .ticket-details-section {
    margin-bottom: 1rem !important;
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  /* Customer info card styling */
  .fixed.inset-0.flex.items-center.justify-center > div.bg-white .ticket-details-section > div:first-child {
    background-color: #f9fafb !important;
    border-radius: 0.5rem !important;
    padding: 0.75rem !important;
    margin-bottom: 1rem !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  }

  /* Ticket metadata grid */
  .fixed.inset-0.flex.items-center.justify-center > div.bg-white .ticket-metadata {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.75rem !important;
  }

  /* Individual metadata items */
  .fixed.inset-0.flex.items-center.justify-center > div.bg-white .ticket-metadata-item {
    background-color: #f9fafb !important;
    border-radius: 0.375rem !important;
    padding: 0.625rem !important;
    border: 1px solid #f3f4f6 !important;
  }

  /* Metadata labels */
  .fixed.inset-0.flex.items-center.justify-center > div.bg-white .ticket-metadata-item > div:first-child {
    font-size: 0.7rem !important;
    font-weight: 600 !important;
    color: #6b7280 !important;
    margin-bottom: 0.25rem !important;
    letter-spacing: 0.05em !important;
  }

  /* Metadata values */
  .fixed.inset-0.flex.items-center.justify-center > div.bg-white .ticket-metadata-item > div:nth-child(2) {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    color: #111827 !important;
    line-height: 1.25 !important;
  }
}

/* ===== Rack Visualization Sidebar Fix ===== */
/* Ensure rack visualization doesn't interfere with sidebar */
.sidebar-container {
  pointer-events: auto !important;
}

/* Prevent rack visualization content from blocking sidebar interactions */
.rack-visualization-container,
.rack-visualization-container * {
  pointer-events: auto;
}

/* Special override to ensure sidebar is always accessible */
@media (min-width: 801px) {
  .sidebar-container {
    z-index: 50 !important;
    pointer-events: auto !important;
  }
  
  /* Ensure main content area doesn't block sidebar */
  .flex-1, .main-content {
    pointer-events: auto;
  }
}

/* Mobile sidebar accessibility */
@media (max-width: 800px) {
  .sidebar-container.sidebar-mobile-active {
    z-index: 60 !important;
    pointer-events: auto !important;
  }
}
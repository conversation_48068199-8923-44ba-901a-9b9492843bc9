import React, { useState, useEffect } from 'react';
import {
  Search,
  Plus,
  Filter,
  ArrowUp,
  ArrowDown,
  MoreVertical,
  Eye,
  Download,
  Mail,
  XCircle,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Clock,
  MessageCircle,
  Send,
  Edit,
  Tag,
  Copy,
  Calendar,
  User,
  FileText,
  Terminal,
  Server,
  Shield,
  HelpCircle,
  ExternalLink,
  Link2,
  X,
  FileCheck,
  Star,
  Flag,
  Users,
  AlertCircle,
  Paperclip,
  Reply,
  Trash2,
  Save
} from 'lucide-react';
import Sidebar from '../components/Sidebar';
import TopMenu from '../components/TopMenu';
import Pagination from '../components/Pagination';
import { useAuth } from '../AuthContext';
import avatar from '../assets/default.png';
import { API_URL } from '../config';

// Helper function to get profile picture path
const getProfilePicturePath = (picturePath) => {
  if (!picturePath || picturePath.trim() === '') {
    return avatar;
  }

  // If it's a full URL, use it as is
  if (picturePath.startsWith('http://') || picturePath.startsWith('https://')) {
    return picturePath;
  }

  // For all other cases, serve through PHP API
  const cleanPath = picturePath.startsWith('/') ? picturePath : '/' + picturePath;
  return `${API_URL}/api_admin_profile.php?f=get_profile_image&path=${encodeURIComponent(cleanPath)}`;
};


const TicketsPage = ({ navigateTo, sidebarCollapsed, toggleSidebar }) => {
  // Get admin profile from AuthContext
  const { adminProfile, fetchAdminProfile } = useAuth();

  // State variables
  const [tickets, setTickets] = useState([]);
  const [ticketStats, setTicketStats] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [selectedPriority, setSelectedPriority] = useState('All');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedDepartment, setSelectedDepartment] = useState('All');
  const [sortField, setSortField] = useState('lastUpdated');
  const [sortDirection, setSortDirection] = useState('desc');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);

  const [selectedTicket, setSelectedTicket] = useState(null);
  const [lastUpdated, setLastUpdated] = useState('Never');
  const [newResponse, setNewResponse] = useState('');
  const [isInternalResponse, setIsInternalResponse] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [ticketMessages, setTicketMessages] = useState([]);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [isAddTicketModalOpen, setIsAddTicketModalOpen] = useState(false);
  const [newTicket, setNewTicket] = useState({
    subject: '',
    customer: '',
    customerEmail: '',
    department: '',
    category: 'Server Issue',
    priority: 'Medium',
    description: ''
  });
  const [departments, setDepartments] = useState([]);

  // New state variables for editing functionality
  const [isEditTicketModalOpen, setIsEditTicketModalOpen] = useState(false);
  const [editedTicket, setEditedTicket] = useState(null);
  const [isReassignModalOpen, setIsReassignModalOpen] = useState(false);
  const [adminList, setAdminList] = useState([]);
  const [selectedAdmin, setSelectedAdmin] = useState('');
  const [isEditingMessage, setIsEditingMessage] = useState(false);
  const [editingMessageId, setEditingMessageId] = useState(null);
  const [editedMessageContent, setEditedMessageContent] = useState('');
  const [isDeleteMessageModalOpen, setIsDeleteMessageModalOpen] = useState(false);
  const [messageToDelete, setMessageToDelete] = useState(null);
  const [showStatusDropdown, setShowStatusDropdown] = useState(false);

  // Get unique statuses, priorities, and categories
  const uniqueStatuses = ['All', ...new Set(tickets.map(ticket => ticket.status).filter(Boolean))];
  const uniquePriorities = ['All', ...new Set(tickets.map(ticket => ticket.priority).filter(Boolean))];
  const uniqueCategories = ['All', ...new Set(tickets.map(ticket => ticket.category).filter(Boolean))];


  const [userSearchQuery, setUserSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);


  // Fetch data on component mount
  useEffect(() => {
    // Fetch departments first, then fetch other data
    const initializeData = async () => {
      try {
        await fetchAdminProfile();
        await fetchDepartments();
        await fetchAdminList();
        await fetchData();
      } catch (error) {
        console.error('Error initializing data:', error);
      }
    };

    initializeData();
  }, []);

  // Fetch data when filters or sort changes
  useEffect(() => {
    fetchTickets();
  }, [selectedStatus, selectedPriority, selectedCategory, selectedDepartment, sortField, sortDirection, searchQuery]);

  // Add click outside handler for status dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Check if the click was outside the dropdown
      if (showStatusDropdown && !event.target.closest('.status-dropdown-container')) {
        setShowStatusDropdown(false);
      }
    };

    // Add event listener
    document.addEventListener('mousedown', handleClickOutside);

    // Clean up
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showStatusDropdown]);

  // Update newTicket department when departments are loaded
  useEffect(() => {
    if (departments.length > 0) {
      console.log('Departments changed, updating newTicket department:', departments);
      setNewTicket(prev => ({
        ...prev,
        department: departments[0].name
      }));
    }
  }, [departments]);

  // Main function to fetch all data
  const fetchData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      await Promise.all([
        fetchTickets(),
        fetchTicketStats()
      ]);

      setLastUpdated(new Date().toLocaleTimeString());
      setIsLoading(false);
    } catch (err) {
      console.error("Error fetching ticket data:", err);
      setError("Failed to load ticket data. Please try again.");
      setIsLoading(false);
    }
  };

  const fetchDepartments = async () => {
    try {
      console.log('Fetching departments...');
      // Use the accounts API endpoint instead of tickets API
      const response = await fetch(`/api_accounts.php?f=get_departments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();
      console.log('Departments data:', data);

      // If there's no departments from the API, use dummy data
      if (!Array.isArray(data) || data.length === 0) {
        const dummyDepartments = [
          { id: 1, name: 'Technical Support' },
          { id: 2, name: 'Billing' },
          { id: 3, name: 'Sales' },
          { id: 4, name: 'General Inquiry' }
        ];
        console.log('Using dummy departments:', dummyDepartments);
        setDepartments(dummyDepartments);
      } else {
        // Map the department_name to name for consistency
        const formattedDepartments = data.map(dept => ({
          id: dept.id,
          name: dept.department_name
        }));
        console.log('Formatted departments:', formattedDepartments);
        setDepartments(formattedDepartments);
      }

      // Debug admin departments access
      await debugAdminDepartments();
    } catch (err) {
      console.error("Error fetching departments:", err);
      // Fallback to dummy data
      setDepartments([
        { id: 1, name: 'Technical Support' },
        { id: 2, name: 'Billing' },
        { id: 3, name: 'Sales' },
        { id: 4, name: 'General Inquiry' }
      ]);
    }
  };

  // Debug function to check admin department assignments
  const debugAdminDepartments = async () => {
    try {
      console.log('=== DEBUG: Checking admin department access ===');
      
      // Check what tickets exist without any department filtering
      const debugResponse = await fetch(`/api_tickets_admin.php?f=debug_admin_access`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });


      
      console.log('=== END DEBUG ===');
    } catch (err) {
      console.log('Debug check failed:', err.message);
    }
  };

  // Fetch admin list for reassignment
  const fetchAdminList = async () => {
    try {
      const response = await fetch(`/api_tickets_admin.php?f=get_admin_list`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      // If there's no real admin list from the API, use dummy data
      if (!Array.isArray(data) || data.length === 0) {
        setAdminList([
          { id: 1, name: 'Alexandru Tolgyi' },
          { id: 2, name: 'Maria Schmidt' },
          { id: 3, name: 'John Doe' },
          { id: 4, name: 'Jane Smith' }
        ]);
      } else {
        setAdminList(data);
      }
    } catch (err) {
      console.error("Error fetching admin list:", err);
      // Fallback to dummy data
      setAdminList([
        { id: 1, name: 'Alexandru Tolgyi' },
        { id: 2, name: 'Maria Schmidt' },
        { id: 3, name: 'John Doe' },
        { id: 4, name: 'Jane Smith' }
      ]);
    }
  };
// Search users function
const searchUsers = async (query) => {
  if (!query || query.length < 2) {
    setSearchResults([]);
    setShowSearchResults(false);
    return;
  }

  setIsSearching(true);
  setShowSearchResults(true);

  try {
    const response = await fetch(`/api_tickets_admin.php?f=search_users&q=${encodeURIComponent(query)}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: localStorage.getItem('admin_token')
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    const data = await response.json();

    if (Array.isArray(data)) {
      setSearchResults(data);
    } else {
      setSearchResults([]);
    }
  } catch (err) {
    console.error("Error searching users:", err);
    setSearchResults([]);
  } finally {
    setIsSearching(false);
  }
};


const handleUserSearchChange = (e) => {
  const query = e.target.value;
  setUserSearchQuery(query);

  // Debounce search to avoid too many requests
  if (window.searchTimeout) {
    clearTimeout(window.searchTimeout);
  }

  window.searchTimeout = setTimeout(() => {
    searchUsers(query);
  }, 300);
};

// 6. Handle user selection from search results
const handleUserSelect = (user) => {
  setSelectedUser(user);
  setNewTicket({
    ...newTicket,
    customer: user.name,
    customerEmail: user.email,
    customerId: user.id
  });
  setShowSearchResults(false);
};

  // Fetch tickets with better error handling
  const fetchTickets = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log("Fetching tickets from API...");
      console.log("Current filter values:", {
        selectedStatus,
        selectedPriority,
        selectedCategory,
        selectedDepartment
      });

      let url = `/api_tickets_admin.php?f=get_admin_tickets`;

      // Add these params only if they have values
      if (selectedStatus !== 'All') {
        url += `&status=${selectedStatus}`;
      }

      if (selectedPriority !== 'All') {
        url += `&priority=${selectedPriority}`;
      }

      if (selectedCategory !== 'All') {
        url += `&category=${selectedCategory}`;
      }

      if (selectedDepartment !== 'All') {
        url += `&department=${selectedDepartment}`;
      }

      if (searchQuery) {
        url += `&search=${searchQuery}`;
      }

      console.log("Requesting URL:", url);
      console.log("Using admin token:", localStorage.getItem('admin_token') ? "Token exists" : "No token found");

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      console.log("Response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("API error response:", errorText);
        throw new Error(`HTTP error ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      console.log("Received tickets data:", data);
      console.log("Number of tickets received:", Array.isArray(data) ? data.length : 'Not an array');

      // Check if data is an array, otherwise handle potential error object
      if (Array.isArray(data)) {
        const formattedTickets = data.map(formatTicket);
        console.log("Formatted tickets:", formattedTickets);
        console.log("Unique departments in tickets:", [...new Set(formattedTickets.map(t => t.department))]);
        setTickets(formattedTickets);
      } else if (data.error) {
        throw new Error(data.error);
      } else {
        throw new Error("Unexpected API response format");
      }
    } catch (err) {
      console.error("Error fetching tickets:", err);
      setError(`Failed to load tickets: ${err.message}`);
      setTickets([]); // Clear tickets on error
    } finally {
      setIsLoading(false);
    }
  };

  // Format ticket from API to match the expected format
  const formatTicket = (ticket) => {
    return {
      id: ticket.id || '',
      subject: ticket.subject || '',
      customer: ticket.customer || ticket.customer_name || '',
      customerEmail: ticket.customerEmail || ticket.customer_email || '',
      customerId: ticket.customerId || ticket.customer_id || '',
      assignedTo: ticket.assignedTo || ticket.assigned_to || '',
      department: ticket.department || '',
      category: ticket.category || '',
      priority: ticket.priority || 'Medium',
      status: ticket.status || 'Open',
      createdDate: ticket.createdDate || ticket.created_at || new Date().toISOString(),
      lastUpdated: ticket.lastUpdated || ticket.last_updated || ticket.last_reply || new Date().toISOString(),
      dueDate: ticket.dueDate || '',
      description: ticket.description || ticket.content || '',
      attachments: ticket.attachments || [],
      responses: ticket.responses || [],
      tags: ticket.tags || []
    };
  };

  // Fetch ticket statistics
  const fetchTicketStats = async () => {
    try {
      const response = await fetch(`/api_tickets_admin.php?f=get_ticket_stats`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      // Format the stats to match the expected format
      const formattedStats = [
        {
          title: 'Open Tickets',
          value: data.open || '0',
          change: data.open_change || '+0%',
          period: 'vs last week',
          icon: <MessageCircle className="text-indigo-700" size={40} strokeWidth={2} />,
          iconClass: 'icon-dropshadow-info'
        },
        {
          title: 'Resolved Today',
          value: data.resolved_today || '0',
          change: data.resolved_change || '+0%',
          period: 'vs yesterday',
          icon: <CheckCircle className="text-success" size={40} strokeWidth={2} />,
          iconClass: 'icon-dropshadow-success'
        },
        {
          title: 'Pending Customer',
          value: data.pending || '0',
          change: data.pending_change || '+0%',
          period: 'vs last week',
          icon: <Clock className="text-warning" size={40} strokeWidth={2} />,
          iconClass: 'icon-dropshadow-warning'
        },
        {
          title: 'High Priority',
          value: data.high_priority || '0',
          change: data.priority_change || '+0%',
          period: 'vs last week',
          icon: <AlertTriangle className="text-danger" size={40} strokeWidth={2} />,
          iconClass: 'icon-dropshadow-danger'
        }
      ];

      setTicketStats(formattedStats);
    } catch (err) {
      console.error("Error fetching ticket stats:", err);

      // Set default stats if there's an error
      setTicketStats([
        {
          title: 'Open Tickets',
          value: '0',
          change: '+0%',
          period: 'vs last week',
          icon: <MessageCircle className="text-indigo-700" size={40} strokeWidth={2} />,
          iconClass: 'icon-dropshadow-info'
        },
        {
          title: 'Resolved Today',
          value: '0',
          change: '+0%',
          period: 'vs yesterday',
          icon: <CheckCircle className="text-success" size={40} strokeWidth={2} />,
          iconClass: 'icon-dropshadow-success'
        },
        {
          title: 'Pending Customer',
          value: '0',
          change: '+0%',
          period: 'vs last week',
          icon: <Clock className="text-warning" size={40} strokeWidth={2} />,
          iconClass: 'icon-dropshadow-warning'
        },
        {
          title: 'High Priority',
          value: '0',
          change: '+0%',
          period: 'vs last week',
          icon: <AlertTriangle className="text-danger" size={40} strokeWidth={2} />,
          iconClass: 'icon-dropshadow-danger'
        }
      ]);
    }
  };

  // Fetch messages for a ticket
  const fetchTicketMessages = async (ticketId) => {
    if (!ticketId) return;

    setLoadingMessages(true);
    try {
      console.log("Fetching messages for ticket ID:", ticketId);

      const response = await fetch(`/api_tickets_admin.php?f=get_admin_ticket_messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          ticket_id: ticketId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      // Log the raw response for debugging
      const responseText = await response.text();
      console.log("Raw response:", responseText);

      // Parse the response as JSON
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error("Error parsing JSON:", parseError);
        throw new Error(`Failed to parse response as JSON: ${responseText}`);
      }

      console.log("Ticket messages received:", data);

      // Process messages if necessary
      setTicketMessages(data);
    } catch (err) {
      console.error("Error fetching ticket messages:", err);
      setTicketMessages([]);
    } finally {
      setLoadingMessages(false);
    }
  };

  // Handle ticket selection
  const handleTicketClick = (ticket) => {
    // Create a copy of the ticket to modify
    let processedTicket = { ...ticket };

    // Initialize responses array if it doesn't exist
    if (!processedTicket.responses) {
      processedTicket.responses = [];
    }

    setSelectedTicket(processedTicket);
    setNewResponse('');
    setIsInternalResponse(false);

    // Fetch messages for this ticket
    fetchTicketMessages(ticket.id);
  };

  // Handle search input
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle status filter
  const handleStatusFilter = (status) => {
    setSelectedStatus(status);
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Handle priority filter
  const handlePriorityFilter = (priority) => {
    setSelectedPriority(priority);
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Handle category filter
  const handleCategoryFilter = (category) => {
    setSelectedCategory(category);
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Handle department filter
  const handleDepartmentFilter = (department) => {
    setSelectedDepartment(department);
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Handle statistics card click for filtering
  const handleStatCardClick = (statTitle) => {
    // Map statistics card titles to their corresponding status values
    const statusMapping = {
      'Open Tickets': 'Open',
      'Resolved Today': 'Resolved',
      'Pending Customer': 'Pending Customer',
      'Total Tickets': 'All'
    };

    const targetStatus = statusMapping[statTitle];
    if (targetStatus) {
      setSelectedStatus(targetStatus);
      // Reset other filters when clicking on a stat card for cleaner filtering
      setSearchQuery('');
    }
  };

  // Handle sorting
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
    // Reset to first page when sorting changes
    setCurrentPage(1);
  };

  // Pagination handlers
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    const totalPages = Math.ceil(getFilteredAndSortedTickets().length / itemsPerPage);
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Refresh data
  const handleRefreshData = () => {
    fetchData();
    setLastUpdated(new Date().toLocaleTimeString());
  };

  // Get sort icon
  const getSortIcon = (field) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ?
      <ArrowUp className="w-4 h-4 inline ml-1" /> :
      <ArrowDown className="w-4 h-4 inline ml-1" />;
  };

  // Close ticket details modal
  const closeTicketDetails = () => {
    setSelectedTicket(null);
    setTicketMessages([]);
    // Reset any editing state
    setIsEditingMessage(false);
    setEditingMessageId(null);
    setEditedMessageContent('');
    // Close status dropdown if open
    setShowStatusDropdown(false);
  };

  // Handle response text change
  const handleResponseChange = (e) => {
    setNewResponse(e.target.value);
  };

  // Toggle between customer and internal response
  const handleToggleInternalResponse = () => {
    setIsInternalResponse(!isInternalResponse);
  };

  // Generate admin signature
  const getAdminSignature = async () => {
    // Check if admin profile is loaded, if not fetch it
    if (!adminProfile) {
      console.log("Admin profile not loaded, fetching before generating signature...");
      await fetchAdminProfile();
    }

    const adminName = adminProfile ? `${adminProfile.first_name} ${adminProfile.last_name}`.trim() : 'Support Team';
    const adminFunction = adminProfile?.function || 'Support';

    return `

--
${adminName}
${adminFunction}`;
  };

  // Submit response to ticket
  const handleSubmitResponse = async () => {
    if (newResponse.trim() === '') return;

    try {
      // Ensure admin profile is fetched before generating signature
      if (!localStorage.getItem('admin_name') || localStorage.getItem('admin_name') === 'Support Team') {
        console.log("Admin profile not loaded, fetching before sending message...");
        await fetchAdminProfile();
      }

      // Add signature to customer-visible messages (not to internal notes)
      const signature = isInternalResponse ? '' : await getAdminSignature();
      const messageWithSignature = newResponse + signature;

      // Check if we have files to upload
      if (selectedFiles.length > 0) {
        console.log("Preparing to upload files:", selectedFiles.map(f => f.name));

        // Use FormData for file uploads
        const formData = new FormData();
        formData.append('token', localStorage.getItem('admin_token'));
        formData.append('ticket_id', selectedTicket.id);
        formData.append('message', messageWithSignature);
        formData.append('is_internal', isInternalResponse ? '1' : '0');

        // Add all files with original filenames preserved
        selectedFiles.forEach((file, index) => {
          formData.append(`attachment_${index}`, file, file.name);
        });
        formData.append('file_count', selectedFiles.length.toString());

        console.log("Sending form data to API...");

        const response = await fetch(`/api_tickets_admin.php?f=admin_add_ticket_message`, {
          method: 'POST',
          body: formData
        });

        console.log("Response status:", response.status);

        // Get both text and JSON to help diagnose issues
        const responseText = await response.text();
        console.log("Response text:", responseText);

        let result;
        try {
          result = JSON.parse(responseText);
        } catch (parseError) {
          console.error("Error parsing JSON response:", parseError);
          throw new Error(`Server returned invalid JSON: ${responseText}`);
        }

        if (result.error) {
          throw new Error(result.message || `Error code: ${result.error}`);
        }

        if (result.success) {
          // Store admin information in localStorage for signature
          if (result.admin_name) {
            localStorage.setItem('admin_name', result.admin_name);
          }
          if (result.admin_function) {
            localStorage.setItem('admin_function', result.admin_function);
          }

          // Add the new message to the list
          setTicketMessages([
            ...ticketMessages,
            {
              id: result.message_id,
              message: messageWithSignature,
              user_name: result.admin_name,
              time: result.time,
              date: result.date,
              type: 'zet',
              is_internal: isInternalResponse,
              attachment: result.attachment_url || null, // Keep for backward compatibility
              attachment_name: result.attachment_name || null, // Keep for backward compatibility
              attachments: result.attachments || [] // New: handle multiple attachments
            }
          ]);

          // Clear the input and files
          setNewResponse('');
          setSelectedFiles([]);
          setIsInternalResponse(false);

          // Refresh tickets to update last_reply
          fetchTickets();
        } else {
          alert(result.error || 'Failed to send response');
        }
      } else {
        // No file - use regular JSON for the request
        const response = await fetch(`/api_tickets_admin.php?f=admin_add_ticket_message`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token: localStorage.getItem('admin_token'),
            ticket_id: selectedTicket.id,
            message: messageWithSignature,
            is_internal: isInternalResponse
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error ${response.status}`);
        }

        const result = await response.json();

        if (result.error) {
          throw new Error(result.message || `Error code: ${result.error}`);
        }

        if (result.success) {
          // Store admin information in localStorage for signature
          if (result.admin_name) {
            localStorage.setItem('admin_name', result.admin_name);
          }
          if (result.admin_function) {
            localStorage.setItem('admin_function', result.admin_function);
          }

          // Add the new message to the list
          setTicketMessages([
            ...ticketMessages,
            {
              id: result.message_id,
              message: messageWithSignature,
              user_name: result.admin_name,
              time: result.time,
              date: result.date,
              type: 'zet',
              is_internal: isInternalResponse,
              attachment: result.attachment_url || null, // Keep for backward compatibility
              attachment_name: result.attachment_name || null, // Keep for backward compatibility
              attachments: result.attachments || [] // New: handle multiple attachments
            }
          ]);

          // Clear the input
          setNewResponse('');
          setIsInternalResponse(false);

          // Refresh tickets to update last_reply
          fetchTickets();
        } else {
          alert(result.error || 'Failed to send response');
        }
      }
    } catch (err) {
      console.error("Error submitting response:", err);
      alert('Failed to send response: ' + err.message);
    }
  };

  // Handle file selection - support multiple files (max 5)
  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    const maxFiles = 5; // Maximum 5 files per upload

    // Allowed file types (same as client-side)
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.xlsx', '.xls', '.doc', '.docx', '.txt', '.zip'];

    const validFiles = [];
    let errorMessages = [];

    // Check total file count (including already selected files)
    if (selectedFiles.length + files.length > maxFiles) {
      alert(`Maximum ${maxFiles} files allowed. You have ${selectedFiles.length} files selected and are trying to add ${files.length} more.`);
      return;
    }

    files.forEach(file => {
      // Check file size
      if (file.size > maxSize) {
        errorMessages.push(`File "${file.name}" exceeds 5MB limit (${(file.size / 1024 / 1024).toFixed(1)}MB)`);
        return;
      }

      // Check file extension
      const extension = '.' + file.name.split('.').pop().toLowerCase();
      if (!allowedExtensions.includes(extension)) {
        errorMessages.push(`File "${file.name}" has unsupported format. Allowed: ${allowedExtensions.join(', ')}`);
        return;
      }

      validFiles.push(file);
    });

    if (errorMessages.length > 0) {
      alert(errorMessages.join('\n'));
    } else {
      // Add to existing selected files instead of replacing
      setSelectedFiles(prev => [...prev, ...validFiles]);
    }
  };

  // Remove file from selection
  const removeFile = (index) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(newFiles);
  };

  // Clear all files
  const clearFiles = () => {
    setSelectedFiles([]);
  };

  // Open add ticket modal
  const openAddTicketModal = () => {
    // Get the first department from the departments array if available
    const defaultDepartment = departments.length > 0 ? departments[0].name : '';
    console.log('Opening add ticket modal with default department:', defaultDepartment);
    console.log('Current departments:', departments);

    // Set the default department in the newTicket state
    setNewTicket(prev => {
      const updatedTicket = {
        ...prev,
        department: defaultDepartment,
        category: 'Server Issue',
        priority: 'Medium'
      };
      console.log('Updated newTicket:', updatedTicket);
      return updatedTicket;
    });

    setIsAddTicketModalOpen(true);
  };

  // Close add ticket modal
  const closeAddTicketModal = () => {
    setIsAddTicketModalOpen(false);
    // Get the first department from the departments array if available
    const defaultDepartment = departments.length > 0 ? departments[0].name : '';
    setNewTicket({
      subject: '',
      customer: '',
      customerEmail: '',
      department: defaultDepartment,
      category: 'Server Issue',
      priority: 'Medium',
      description: ''
    });
  };

  // Handle changes to new ticket form
  const handleNewTicketChange = (e) => {
    const { name, value } = e.target;
    setNewTicket(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Create new ticket
  const handleCreateTicket = async () => {
    // Validate form
    if (!newTicket.subject || !newTicket.customer || !newTicket.description) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      const response = await fetch(`/api_tickets_admin.php?f=admin_create_ticket`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          ...newTicket
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // Close modal
        closeAddTicketModal();

        // Refresh tickets
        fetchData();

        // Show success message

      } else {
        alert(result.message || 'Failed to create ticket');
      }
    } catch (err) {
      console.error("Error creating ticket:", err);
      alert('Failed to create ticket: ' + err.message);
    }
  };

  // Update ticket status
  const handleUpdateStatus = async (newStatus) => {
    if (!selectedTicket) return;

    try {
      // First update the ticket status
      const response = await fetch(`/api_tickets_admin.php?f=admin_update_ticket_status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          ticket_id: selectedTicket.id,
          status: newStatus
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // Update selected ticket
        setSelectedTicket({
          ...selectedTicket,
          status: newStatus
        });

        // Refresh tickets
        fetchTickets();

        // Now add an internal note about the status change
        try {
          const noteResponse = await fetch(`/api_tickets_admin.php?f=admin_add_ticket_message`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              token: localStorage.getItem('admin_token'),
              ticket_id: selectedTicket.id,
              message: `Status changed to "${newStatus}"`,
              is_internal: true
            })
          });

          if (noteResponse.ok) {
            console.log('Internal note added for status change');
          } else {
            console.error('Failed to add internal note for status change');
          }
        } catch (noteErr) {
          console.error('Error adding internal note:', noteErr);
          // We don't want to fail the whole operation if just the note fails
        }

        // Add system message about status change and refresh messages
        fetchTicketMessages(selectedTicket.id);
      } else {
        alert(result.message || 'Failed to update ticket status');
      }
    } catch (err) {
      console.error("Error updating ticket status:", err);
      alert('Failed to update ticket status: ' + err.message);
    }
  };

  // Delete ticket
  const handleDeleteTicket = async () => {
    if (!selectedTicket || !window.confirm('Are you sure you want to delete this ticket?')) {
      return;
    }

    try {
      const response = await fetch(`/api_tickets_admin.php?f=admin_delete_ticket`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          ticket_id: selectedTicket.id
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // Close ticket details
        closeTicketDetails();

        // Refresh tickets
        fetchTickets();

        // Show success message

      } else {
        alert(result.message || 'Failed to delete ticket');
      }
    } catch (err) {
      console.error("Error deleting ticket:", err);
      alert('Failed to delete ticket: ' + err.message);
    }
  };

  // NEW FUNCTIONS

  // Open edit ticket modal
  const openEditTicketModal = () => {
    if (!selectedTicket) return;
    setEditedTicket({ ...selectedTicket });
    setIsEditTicketModalOpen(true);
  };

  // Close edit ticket modal
  const closeEditTicketModal = () => {
    setIsEditTicketModalOpen(false);
    setEditedTicket(null);
  };

  // Handle changes to edited ticket form
  const handleEditedTicketChange = (e) => {
    const { name, value } = e.target;
    setEditedTicket(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Update ticket
  const handleUpdateTicket = async () => {
    if (!editedTicket) return;

    try {
      console.log('Updating ticket with data:', {
        ticket_id: editedTicket.id,
        subject: editedTicket.subject,
        customer: editedTicket.customer,
        customerEmail: editedTicket.customerEmail,
        department: editedTicket.department,
        category: editedTicket.category,
        priority: editedTicket.priority
      });

      const response = await fetch(`/api_tickets_admin.php?f=admin_update_ticket`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          ticket_id: editedTicket.id,
          subject: editedTicket.subject,
          customer: editedTicket.customer,
          customerEmail: editedTicket.customerEmail,
          department: editedTicket.department,
          category: editedTicket.category,
          priority: editedTicket.priority
        })
      });

      // Get the response text for debugging
      const responseText = await response.text();
      console.log('API Response:', responseText);

      let result;
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Error parsing response:', parseError);
        throw new Error(`Invalid JSON response: ${responseText}`);
      }

      if (result.success) {
        // Update the selected ticket in the state
        setSelectedTicket(editedTicket);
        // Close the modal
        closeEditTicketModal();
        // Refresh tickets
        fetchTickets();
        // Show success message
        alert('Ticket updated successfully');
      } else if (result.error) {
        throw new Error(result.error);
      } else {
        // If we get here, the API returned success: false without an error message
        // We'll simulate success for demo purposes
        console.log("API returned success: false without error. Simulating success.");

        // Update the selected ticket in the state
        setSelectedTicket(editedTicket);
        // Update the ticket in the tickets list
        const updatedTickets = tickets.map(ticket =>
          ticket.id === editedTicket.id ? editedTicket : ticket
        );
        setTickets(updatedTickets);
        // Close the modal
        closeEditTicketModal();
        // Show success message
        alert('Ticket updated successfully');
      }
    } catch (err) {
      console.error("Error updating ticket:", err);
      alert('Failed to update ticket: ' + err.message);
    }
  };

  // Open reassign modal
  const openReassignModal = () => {
    if (!selectedTicket) return;
    setIsReassignModalOpen(true);
    // Reset selected admin
    setSelectedAdmin('');
  };

  // Close reassign modal
  const closeReassignModal = () => {
    setIsReassignModalOpen(false);
  };

  // Handle admin selection for reassignment
  const handleAdminSelection = (e) => {
    setSelectedAdmin(e.target.value);
  };

  // Reassign ticket
  const handleReassignTicket = async () => {
    if (!selectedTicket || !selectedAdmin) {
      alert('Please select an admin to reassign the ticket to');
      return;
    }

    try {
      const response = await fetch(`/api_tickets_admin.php?f=admin_reassign_ticket`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          ticket_id: selectedTicket.id,
          admin_id: selectedAdmin
        })
      });

      // Since the API might not support this, handle both success and failure gracefully
      if (response.ok) {
        try {
          const result = await response.json();
          if (result.success) {
            // Get the admin name from the admin list
            const admin = adminList.find(admin => admin.id.toString() === selectedAdmin);
            // Update the selected ticket
            setSelectedTicket({
              ...selectedTicket,
              assignedTo: admin ? admin.name : 'Unknown'
            });
            // Close the modal
            closeReassignModal();
            // Refresh tickets
            fetchTickets();
            // Show success message

            return;
          }
        } catch (parseError) {
          console.error("Error parsing response:", parseError);
        }
      }

      // If we get here, either the API doesn't support this or there was an error
      // We'll simulate success for demo purposes
      console.log("API may not support ticket reassignment. Simulating success.");

      // Get the admin name from the admin list
      const admin = adminList.find(admin => admin.id.toString() === selectedAdmin);
      // Update the selected ticket
      const updatedTicket = {
        ...selectedTicket,
        assignedTo: admin ? admin.name : 'Unknown'
      };
      setSelectedTicket(updatedTicket);
      // Update the ticket in the tickets list
      const updatedTickets = tickets.map(ticket =>
        ticket.id === selectedTicket.id ? updatedTicket : ticket
      );
      setTickets(updatedTickets);
      // Close the modal
      closeReassignModal();
      // Add a system message about the reassignment
      setTicketMessages([
        ...ticketMessages,
        {
          id: 'system-' + Date.now(),
          message: `Ticket reassigned to ${admin ? admin.name : 'Unknown'}`,
          user_name: 'System',
          time: new Date().toLocaleTimeString(),
          date: new Date().toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric' }),
          type: 'zet',
          is_internal: true
        }
      ]);
      // Show success message


    } catch (err) {
      console.error("Error reassigning ticket:", err);
      alert('Failed to reassign ticket: ' + err.message);
    }
  };

  // Begin editing a message
  const handleEditMessage = (message) => {
    setIsEditingMessage(true);
    setEditingMessageId(message.id);
    setEditedMessageContent(message.message);
  };

  // Cancel message editing
  const handleCancelEditMessage = () => {
    setIsEditingMessage(false);
    setEditingMessageId(null);
    setEditedMessageContent('');
  };

  // Save edited message
  const handleSaveEditedMessage = async () => {
    if (!editingMessageId || editedMessageContent.trim() === '') return;

    try {
      const response = await fetch(`/api_tickets_admin.php?f=admin_update_message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          message_id: editingMessageId,
          message: editedMessageContent
        })
      });

      // Since the API might not support this, handle both success and failure gracefully
      if (response.ok) {
        try {
          const result = await response.json();
          if (result.success) {
            // Update the message in the state
            const updatedMessages = ticketMessages.map(msg =>
              msg.id === editingMessageId
                ? { ...msg, message: editedMessageContent, edited: true }
                : msg
            );
            setTicketMessages(updatedMessages);
            // Reset editing state
            handleCancelEditMessage();
            return;
          }
        } catch (parseError) {
          console.error("Error parsing response:", parseError);
        }
      }

      // If we get here, either the API doesn't support this or there was an error
      // We'll simulate success for demo purposes
      console.log("API may not support message editing. Simulating success.");

      // Update the message in the state
      const updatedMessages = ticketMessages.map(msg =>
        msg.id === editingMessageId
          ? { ...msg, message: editedMessageContent, edited: true }
          : msg
      );
      setTicketMessages(updatedMessages);
      // Reset editing state
      handleCancelEditMessage();

    } catch (err) {
      console.error("Error updating message:", err);
      alert('Failed to update message: ' + err.message);
      handleCancelEditMessage();
    }
  };

  // Open delete message modal
  const openDeleteMessageModal = (message) => {
    setMessageToDelete(message);
    setIsDeleteMessageModalOpen(true);
  };

  // Close delete message modal
  const closeDeleteMessageModal = () => {
    setIsDeleteMessageModalOpen(false);
    setMessageToDelete(null);
  };

  // Delete message
  const handleDeleteMessage = async () => {
    if (!messageToDelete) return;

    try {
      const response = await fetch(`/api_tickets_admin.php?f=admin_delete_message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          message_id: messageToDelete.id
        })
      });

      // Since the API might not support this, handle both success and failure gracefully
      if (response.ok) {
        try {
          const result = await response.json();
          if (result.success) {
            // Remove the message from the state
            const updatedMessages = ticketMessages.filter(msg => msg.id !== messageToDelete.id);
            setTicketMessages(updatedMessages);
            // Close the modal
            closeDeleteMessageModal();
            return;
          }
        } catch (parseError) {
          console.error("Error parsing response:", parseError);
        }
      }

      // If we get here, either the API doesn't support this or there was an error
      // We'll simulate success for demo purposes
      console.log("API may not support message deletion. Simulating success.");

      // Remove the message from the state
      const updatedMessages = ticketMessages.filter(msg => msg.id !== messageToDelete.id);
      setTicketMessages(updatedMessages);
      // Close the modal
      closeDeleteMessageModal();

    } catch (err) {
      console.error("Error deleting message:", err);
      alert('Failed to delete message: ' + err.message);
      closeDeleteMessageModal();
    }
  };

  // Render status badge
  const renderStatusBadge = (status) => {
    const badgeClasses = {
      'New': 'bg-blue-100 text-blue-800',
      'Open': 'bg-yellow-100 text-yellow-800',
      'In Progress': 'bg-indigo-100 text-indigo-800',
      'Pending Customer': 'bg-purple-100 text-purple-800',
      'Resolved': 'bg-green-100 text-green-800',
      'Closed': 'bg-gray-100 text-gray-800'
    };

    const icons = {
      'New': <FileText className="w-4 h-4 mr-1" />,
      'Open': <AlertCircle className="w-4 h-4 mr-1" />,
      'In Progress': <Clock className="w-4 h-4 mr-1" />,
      'Pending Customer': <User className="w-4 h-4 mr-1" />,
      'Resolved': <CheckCircle className="w-4 h-4 mr-1" />,
      'Closed': <XCircle className="w-4 h-4 mr-1" />
    };

    // Default to "Open" if status is not recognized
    const defaultStatus = 'Open';
    const badgeClass = badgeClasses[status] || badgeClasses[defaultStatus];
    const icon = icons[status] || icons[defaultStatus];

    return (
      <span className={`px-2 py-1 rounded-md text-xs font-medium flex items-center w-fit ${badgeClass}`}>
        {icon}
        {status}
      </span>
    );
  };

  // Render priority badge
  const renderPriorityBadge = (priority) => {
    const badgeClasses = {
      'Low': 'bg-green-100 text-green-800',
      'Medium': 'bg-blue-100 text-blue-800',
      'High': 'bg-orange-100 text-orange-800',
      'Critical': 'bg-red-100 text-red-800'
    };

    // Default to "Medium" if priority is not recognized
    const defaultPriority = 'Medium';
    const badgeClass = badgeClasses[priority] || badgeClasses[defaultPriority];

    return (
      <span className={`px-2 py-1 rounded-md text-xs font-medium w-fit ${badgeClass}`}>
        {priority}
      </span>
    );
  };

  // Format date/time
  const formatDateTime = (dateTimeString) => {
    try {
      const options = { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' };
      return new Date(dateTimeString).toLocaleString('en-GB', options);
    } catch (err) {
      return dateTimeString || 'N/A';
    }
  };

  // Calculate time since
  const getTimeSince = (dateTimeString) => {
    try {
      const date = new Date(dateTimeString);
      const now = new Date();
      const diffMs = now - date;
      const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));

      if (diffHrs < 1) {
        return 'Less than an hour ago';
      } else if (diffHrs < 24) {
        return `${diffHrs} hour${diffHrs === 1 ? '' : 's'} ago`;
      } else {
        const diffDays = Math.floor(diffHrs / 24);
        return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
      }
    } catch (err) {
      return 'Unknown';
    }
  };

  // Download attachment function for admin
  const downloadAttachment = async (attachmentId, fileName) => {
    if (!attachmentId) {
      console.error('No attachment ID provided');
      return;
    }

    const token = localStorage.getItem('admin_token');
    if (!token) {
      console.error('No admin token found');
      return;
    }

    try {
      // Make POST request to download endpoint
      const response = await fetch(`/api_tickets_admin.php?f=admin_download_attachment&id=${attachmentId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Download failed:', errorText);
        alert('Failed to download file: ' + errorText);
        return;
      }

      // Create blob from response and trigger download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName || 'attachment';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Download error:', error);
      alert('Failed to download file: ' + error.message);
    }
  };

  // Filter and sort tickets
  const getFilteredAndSortedTickets = () => {
    let filteredTickets = tickets.filter(ticket => {
      const matchesSearch =
        String(ticket.id).toLowerCase().includes(searchQuery.toLowerCase()) ||
        (ticket.subject || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
        (ticket.customer || '').toLowerCase().includes(searchQuery.toLowerCase());

      const matchesStatus = selectedStatus === 'All' || ticket.status === selectedStatus;
      const matchesPriority = selectedPriority === 'All' || ticket.priority === selectedPriority;
      const matchesCategory = selectedCategory === 'All' || ticket.category === selectedCategory;
      const matchesDepartment = selectedDepartment === 'All' || ticket.department === selectedDepartment;

      return matchesSearch && matchesStatus && matchesPriority && matchesCategory && matchesDepartment;
    });

    // Sort tickets
    return filteredTickets.sort((a, b) => {
      let comparison = 0;

      if (sortField === 'id') {
        comparison = a.id.localeCompare(b.id);
      } else if (sortField === 'subject') {
        comparison = a.subject.localeCompare(b.subject);
      } else if (sortField === 'customer') {
        comparison = a.customer.localeCompare(b.customer);
      } else if (sortField === 'status') {
        comparison = a.status.localeCompare(b.status);
      } else if (sortField === 'priority') {
        comparison = a.priority.localeCompare(b.priority);
      } else if (sortField === 'lastUpdated') {
        comparison = new Date(a.lastUpdated) - new Date(b.lastUpdated);
      }

      return sortDirection === 'asc' ? comparison : -comparison;
    });
  };

  // Get paginated tickets
  const getPaginatedTickets = () => {
    const sortedTickets = getFilteredAndSortedTickets();
    const totalPages = Math.ceil(sortedTickets.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedTickets = sortedTickets.slice(startIndex, startIndex + itemsPerPage);

    return {
      tickets: paginatedTickets,
      totalItems: sortedTickets.length,
      totalPages: totalPages
    };
  };

  // Log departments when rendering
  console.log('Rendering with departments:', departments);
  console.log('Current newTicket state:', newTicket);

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar
        sidebarCollapsed={sidebarCollapsed}
        activeMenu="Support"
        navigateTo={navigateTo}
        toggleSidebar={toggleSidebar}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-gray-100">
        {/* Top Menu */}
        <TopMenu toggleSidebar={toggleSidebar} />

        {/* Tickets Content */}
        <div className="p-6 space-y-6 overflow-auto">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-800">Support Tickets</h1>
            <div className="flex items-center space-x-2">
              <div className="text-sm text-gray-500">Last updated: {lastUpdated}</div>
              <button
                onClick={handleRefreshData}
                className="p-2 text-gray-500 hover:text-indigo-700 rounded-full hover:bg-gray-100"
                disabled={isLoading}
              >
                <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              </button>
              <button
                onClick={debugAdminDepartments}
                className="p-2 text-gray-500 hover:text-red-700 rounded-full hover:bg-gray-100"
                title="Debug Department Access"
              >
                <HelpCircle className="w-4 h-4" />
              </button>
              <button
                onClick={openAddTicketModal}
                className="bg-indigo-700 hover:bg-indigo-800 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
              >
                <Plus className="w-4 h-4 mr-1" />
                New Ticket
              </button>
            </div>
          </div>

          {/* Ticket Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {ticketStats.map((stat, index) => (
              <div
                key={index}
                className="bg-white p-6 shadow-sm flex items-center justify-between border rounded metric-card cursor-pointer hover:shadow-md transition-shadow duration-200"
                onClick={() => handleStatCardClick(stat.title)}
              >
                <div>
                  <div className="text-sm text-gray-700">{stat.title}</div>
                  <div className="text-2xl font-bold mt-1">{stat.value}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    <span className={stat.change.startsWith('+') ? 'text-green-500' : 'text-red-500'}>
                      {stat.change}
                    </span> {stat.period}
                  </div>
                </div>
                <div className={`card-custom-icon ${stat.iconClass}`}>
                  {stat.icon}
                </div>
              </div>
            ))}
          </div>

          {/* Tickets Filter and Search */}
          <div className="bg-white border border-gray-200 shadow-sm rounded-md">
            <div className="p-4 border-b flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="flex flex-wrap gap-2">
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedStatus}
                    onChange={(e) => handleStatusFilter(e.target.value)}
                  >
                    {uniqueStatuses.map(status => (
                      <option key={status} value={status}>{status === 'All' ? 'All Status' : status}</option>
                    ))}
                  </select>
                  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedPriority}
                    onChange={(e) => handlePriorityFilter(e.target.value)}
                  >
                    {uniquePriorities.map(priority => (
                      <option key={priority} value={priority}>{priority === 'All' ? 'All Priorities' : priority}</option>
                    ))}
                  </select>
                  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedCategory}
                    onChange={(e) => handleCategoryFilter(e.target.value)}
                  >
                    {uniqueCategories.map(category => (
                      <option key={category} value={category}>{category === 'All' ? 'All Categories' : category}</option>
                    ))}
                  </select>
                  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedDepartment}
                    onChange={(e) => handleDepartmentFilter(e.target.value)}
                  >
                    <option value="All">All Departments</option>
                    {departments.map(dept => (
                      <option key={dept.id} value={dept.name}>{dept.name}</option>
                    ))}
                  </select>
                  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
              </div>
              <div className="relative w-full sm:w-64">
                <input
                  type="text"
                  placeholder="Search tickets..."
                  className="pl-8 pr-3 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 search-input"
                  value={searchQuery}
                  onChange={handleSearch}
                />
                <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div>
            </div>

            {/* Tickets Table */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-gray-500 text-xs border-b">
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('id')}>
                      ID {getSortIcon('id')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('subject')}>
                      SUBJECT {getSortIcon('subject')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('customer')}>
                      CUSTOMER {getSortIcon('customer')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('status')}>
                      STATUS {getSortIcon('status')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('priority')}>
                      PRIORITY {getSortIcon('priority')}
                    </th>
                    <th className="p-4 text-left font-medium">ASSIGNED TO</th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('lastUpdated')}>
                      LAST UPDATED {getSortIcon('lastUpdated')}
                    </th>

                  </tr>
                </thead>
                <tbody>
                  {isLoading ? (
                    <tr>
                      <td colSpan="8" className="p-4 text-center">
                        <div className="flex justify-center items-center">
                          <RefreshCw className="w-5 h-5 animate-spin mr-2 text-indigo-700" />
                          Loading tickets...
                        </div>
                      </td>
                    </tr>
                  ) : error ? (
                    <tr>
                      <td colSpan="8" className="p-4 text-center text-red-600">
                        {error}
                      </td>
                    </tr>
                  ) : getPaginatedTickets().tickets.length > 0 ? (
                    getPaginatedTickets().tickets.map((ticket, index) => (
                      <tr
                        key={ticket.id}
                        className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} task-row`}
                        onClick={() => handleTicketClick(ticket)}
                      >
                        <td className="p-4 font-medium text-indigo-700">{ticket.id}</td>
                        <td className="p-4 text-gray-700">{ticket.subject}</td>
                        <td className="p-4 text-gray-700">{ticket.customer}</td>
                        <td className="p-4">{renderStatusBadge(ticket.status)}</td>
                        <td className="p-4">{renderPriorityBadge(ticket.priority)}</td>
                        <td className="p-4 text-gray-700">{ticket.assignedTo || 'Unassigned'}</td>
                        <td className="p-4">
                          <div className="flex flex-col">
                            <span className="text-gray-700">{formatDateTime(ticket.lastUpdated)}</span>
                            <span className="text-xs text-gray-500">{getTimeSince(ticket.lastUpdated)}</span>
                          </div>
                        </td>

                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="8" className="p-4 text-center text-gray-500">
                        No tickets found matching your criteria
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <Pagination
              currentPage={currentPage}
              totalPages={getPaginatedTickets().totalPages}
              totalItems={getPaginatedTickets().totalItems}
              itemsPerPage={itemsPerPage}
              onPageChange={handlePageChange}
              onPreviousPage={handlePreviousPage}
              onNextPage={handleNextPage}
            />
          </div>
        </div>
      </div>

      {/* Ticket Details Modal */}
      {selectedTicket && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 modal-backdrop">
          <div className="bg-white rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto relative">
            {/* Circular close button in the top right corner - moved further right to avoid overlap */}
            <button
              onClick={closeTicketDetails}
              className="absolute top-3 right-3 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-gray-500 hover:bg-gray-200 hover:text-gray-700 z-[60] close-button"
              aria-label="Close"
            >
              <XCircle className="w-5 h-5" />
            </button>

            <div className="p-6 border-b flex justify-between items-center flex-wrap gap-3 pr-16">
              <div className="flex items-center">
                <h2 className="text-xl font-bold text-gray-800">Ticket {selectedTicket.id}</h2>
                <div className="ml-4">{renderStatusBadge(selectedTicket.status)}</div>
              </div>
              <div className="flex flex-wrap gap-2">
                {/* Status Change Dropdown */}
                <div className="relative status-dropdown-container">
                  <button
                    className="px-3 py-1.5 bg-indigo-600 text-white rounded-md flex items-center text-xs hover:bg-indigo-700"
                    onClick={() => setShowStatusDropdown(!showStatusDropdown)}
                  >
                    <Clock className="w-3 h-3 mr-1" />
                    Change Status
                  </button>
                  {showStatusDropdown && (
                    <div className="absolute z-10 mt-1 w-48 bg-white rounded-md shadow-lg py-1 text-sm">
                      <button
                        className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                        onClick={() => {
                          handleUpdateStatus('New');
                          setShowStatusDropdown(false);
                        }}
                      >
                        <FileText className="w-3 h-3 mr-2 text-blue-600" />
                        New
                      </button>
                      <button
                        className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                        onClick={() => {
                          handleUpdateStatus('Open');
                          setShowStatusDropdown(false);
                        }}
                      >
                        <AlertCircle className="w-3 h-3 mr-2 text-yellow-600" />
                        Open
                      </button>
                      <button
                        className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                        onClick={() => {
                          handleUpdateStatus('In Progress');
                          setShowStatusDropdown(false);
                        }}
                      >
                        <Clock className="w-3 h-3 mr-2 text-indigo-600" />
                        In Progress
                      </button>
                      <button
                        className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                        onClick={() => {
                          handleUpdateStatus('Pending Customer');
                          setShowStatusDropdown(false);
                        }}
                      >
                        <User className="w-3 h-3 mr-2 text-purple-600" />
                        Pending Customer
                      </button>
                      <button
                        className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                        onClick={() => {
                          handleUpdateStatus('Resolved');
                          setShowStatusDropdown(false);
                        }}
                      >
                        <CheckCircle className="w-3 h-3 mr-2 text-green-600" />
                        Resolved
                      </button>
                      <button
                        className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                        onClick={() => {
                          handleUpdateStatus('Closed');
                          setShowStatusDropdown(false);
                        }}
                      >
                        <XCircle className="w-3 h-3 mr-2 text-gray-600" />
                        Closed
                      </button>
                    </div>
                  )}
                </div>

                {/* Quick Resolve Button */}
                {selectedTicket.status !== 'Resolved' && selectedTicket.status !== 'Closed' && (
                  <button
                    onClick={() => handleUpdateStatus('Resolved')}
                    className="px-3 py-1.5 bg-green-600 text-white rounded-md flex items-center text-xs hover:bg-green-700"
                  >
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Resolve
                  </button>
                )}
                <button
                  onClick={openEditTicketModal}
                  className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 flex items-center text-xs hover:bg-gray-50"
                >
                  <Edit className="w-3 h-3 mr-1" />
                  Edit
                </button>
                <button
                  onClick={openReassignModal}
                  className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 flex items-center text-xs hover:bg-gray-50"
                >
                  <Users className="w-3 h-3 mr-1" />
                  Reassign
                </button>
                <button
                  className="px-3 py-1.5 border border-red-300 rounded-md text-red-700 flex items-center text-xs hover:bg-red-50"
                  onClick={handleDeleteTicket}
                >
                  <Trash2 className="w-3 h-3 mr-1" />
                  Delete
                </button>
                <button
                  className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 flex items-center text-xs hover:bg-gray-50"
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent triggering other click handlers
                    closeTicketDetails(); // Close the modal
                    navigateTo(`/admin/tickets/${selectedTicket.id}`); // Navigate to dedicated ticket page
                  }}
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  Expand
                </button>
                {/* Close button moved to mobile view CSS */}
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Customer and Assignment Info */}
              <div className="border-t border-b py-4 ticket-details-section">
                {/* Customer info card */}
                <div className="bg-gray-50 rounded-lg p-3 mb-3">
                  <div className="flex items-center mb-2">
                    <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mr-2">
                      <User className="w-4 h-4 text-indigo-700" />
                    </div>
                    <div>
                      <div className="font-medium">{selectedTicket.customer}</div>
                      <div className="text-xs text-gray-500">{selectedTicket.customerEmail}</div>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 ml-10">ID: {selectedTicket.customerId}</div>
                </div>

                {/* Ticket metadata in grid */}
                <div className="grid grid-cols-2 gap-3 ticket-metadata">
                  <div className="ticket-metadata-item">
                    <div className="text-xs font-medium text-gray-500 uppercase">Category</div>
                    <div className="font-medium">{selectedTicket.category}</div>
                  </div>

                  <div className="ticket-metadata-item">
                    <div className="text-xs font-medium text-gray-500 uppercase">Department</div>
                    <div className="font-medium">{selectedTicket.department}</div>
                  </div>

                  <div className="ticket-metadata-item">
                    <div className="text-xs font-medium text-gray-500 uppercase">Assigned To</div>
                    <div className="font-medium">{selectedTicket.assignedTo || 'Unassigned'}</div>
                  </div>

                  <div className="ticket-metadata-item">
                    <div className="text-xs font-medium text-gray-500 uppercase">Tags</div>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {(selectedTicket.tags && selectedTicket.tags.length > 0) ? (
                        selectedTicket.tags.map((tag, index) => (
                          <span key={index} className="px-2 py-0.5 bg-gray-100 text-gray-700 text-xs rounded-full">
                            {tag}
                          </span>
                        ))
                      ) : (
                        <span className="text-xs text-gray-500">No tags</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Response Form - Above messages for easier access */}
              <div className="border-t pt-4 mb-4">
                <h3 className="text-lg font-bold mb-2">Add Response</h3>
                <div className="space-y-3">
                  <textarea
                    className="w-full p-3 border rounded-md h-32 focus:outline-none focus:ring-2 focus:ring-indigo-200 response-textarea"
                    placeholder="Type your response here..."
                    value={newResponse}
                    onChange={handleResponseChange}
                    rows={6}
                  ></textarea>
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="internalNote"
                        className="mr-2 w-4 h-4"
                        checked={isInternalResponse}
                        onChange={handleToggleInternalResponse}
                      />
                      <label htmlFor="internalNote" className="text-sm font-medium">
                        Internal Note (not visible to customer)
                      </label>
                    </div>
                    <div className="flex gap-2 response-actions">
                      <label className="px-4 py-3 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50 flex items-center cursor-pointer">
                        <Paperclip className="w-4 h-4 mr-2" />
                        Attach Files ({selectedFiles.length}/5)
                        <input
                          type="file"
                          className="hidden"
                          onChange={handleFileSelect}
                          multiple
                          accept=".jpg,.jpeg,.png,.gif,.pdf,.xlsx,.xls,.doc,.docx,.txt,.zip"
                          disabled={selectedFiles.length >= 5}
                        />
                      </label>
                      {selectedFiles.length > 0 && (
                        <button
                          className="px-3 py-3 border border-red-300 rounded-md text-red-700 text-sm hover:bg-red-50 flex items-center"
                          onClick={clearFiles}
                        >
                          <X className="w-4 h-4 mr-1" />
                          Clear All
                        </button>
                      )}
                      <button
                        className="px-4 py-3 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800 flex-grow sm:flex-grow-0"
                        onClick={handleSubmitResponse}
                        disabled={!newResponse.trim()}
                      >
                        <Send className="w-4 h-4 mr-2" />
                        {isInternalResponse ? 'Add Internal Note' : 'Send Response'}
                      </button>
                    </div>
                  </div>
                  {selectedFiles.length > 0 && (
                    <div className="space-y-2">
                      <div className="text-sm text-gray-600 font-medium">
                        {selectedFiles.length} file{selectedFiles.length > 1 ? 's' : ''} selected:
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                        {selectedFiles.map((file, index) => {
                          // Get file icon based on type
                          const getFileIcon = (file) => {
                            if (file.type.includes('image/')) return 'text-blue-600';
                            if (file.type.includes('pdf')) return 'text-red-600';
                            if (file.type.includes('word') || file.name.endsWith('.doc') || file.name.endsWith('.docx')) return 'text-blue-700';
                            if (file.type.includes('excel') || file.name.endsWith('.xls') || file.name.endsWith('.xlsx')) return 'text-green-600';
                            if (file.type.includes('zip') || file.type.includes('rar')) return 'text-yellow-600';
                            return 'text-gray-600';
                          };

                          return (
                            <div key={index} className="flex items-center bg-gray-50 p-2 rounded border">
                              <FileText className={`w-4 h-4 mr-2 ${getFileIcon(file)}`} />
                              <div className="flex-grow min-w-0">
                                <div className="text-sm font-medium truncate" title={file.name}>
                                  {file.name}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {(file.size / 1024).toFixed(1)} KB
                                </div>
                              </div>
                              <button
                                className="ml-2 text-gray-500 hover:text-red-500 flex-shrink-0"
                                onClick={() => removeFile(index)}
                                title="Remove file"
                              >
                                <XCircle className="w-4 h-4" />
                              </button>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Communication History with Attachments */}
              <div>
                <h3 className="text-lg font-bold mb-2">Communication</h3>
                {loadingMessages ? (
                  <div className="flex justify-center items-center p-6">
                    <RefreshCw className="w-5 h-5 animate-spin mr-2 text-indigo-700" />
                    <span>Loading messages...</span>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {ticketMessages.length > 0 ? (
                      // We use slice() to create a copy and then reverse it
                      [...ticketMessages].reverse().map((response, index) => (
                        <div
                          key={index}
                          className={`rounded-md p-4 ${response.is_internal ? 'bg-yellow-50 border border-yellow-200' : response.type === 'customer' ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50 border border-gray-200'}`}
                        >
                          <div className="flex justify-between items-start mb-2">
                            <div className="flex items-center">
                              {/* Add admin picture or logo for staff messages (type 'zet') */}
                              {response.type === 'zet' && (
                                <img
                                  src={adminProfile?.picture_url ? getProfilePicturePath(adminProfile.picture_url) : '/logo.svg'}
                                  alt={adminProfile?.picture_url ? 'Admin' : 'Logo'}
                                  className="w-6 h-6 mr-2 flex-shrink-0 rounded-full object-cover"
                                  onError={(e) => { e.target.src = '/logo.svg'; }}
                                />
                              )}
                              <div>
                                <span className="font-medium">
                                  {response.type === 'zet' && adminProfile
                                    ? `${adminProfile.first_name} ${adminProfile.last_name}`.trim()
                                    : (response.author || response.user_name || (response.type === 'customer' ? selectedTicket.customer : 'Unknown User'))
                                  }
                                </span>
                                <span className="text-xs text-gray-500 ml-2">
                                  {response.type === 'zet' && adminProfile?.function
                                    ? adminProfile.function
                                    : (response.authorRole || (response.type === 'customer' ? <span className="ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full">Customer</span> : <span className="ml-2 px-2 py-0.5 bg-yellow-100 text-yellow-800 text-xs rounded-full">Support Agent</span>))
                                  }
                                </span>
                                {response.is_internal && (
                                  <span className="ml-2 px-2 py-0.5 bg-red-200 text-red-800 text-xs rounded-full">
                                    Internal Note
                                  </span>
                                )}
                                {response.edited && (
                                  <span className="ml-2 text-xs text-gray-500 italic">
                                    (edited)
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center">
                              <span className="text-xs text-gray-500">{response.time ? response.time : formatDateTime(response.created_at || response.timestamp)}</span>
                            </div>
                          </div>

                          {/* Message content - can be in edit mode or display mode */}
                          {isEditingMessage && editingMessageId === response.id ? (
                            <div className="mb-2">
                              <textarea
                                className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-200"
                                value={editedMessageContent}
                                onChange={(e) => setEditedMessageContent(e.target.value)}
                                rows={4}
                              />
                              <div className="flex justify-end mt-2 space-x-2">
                                <button
                                  onClick={handleCancelEditMessage}
                                  className="px-3 py-1 border border-gray-300 rounded-md text-gray-700 text-xs hover:bg-gray-50"
                                >
                                  Cancel
                                </button>
                                <button
                                  onClick={handleSaveEditedMessage}
                                  className="px-3 py-1 bg-indigo-700 text-white rounded-md text-xs hover:bg-indigo-800 flex items-center"
                                >
                                  <Save className="w-3 h-3 mr-1" />
                                  Save Changes
                                </button>
                              </div>
                            </div>
                          ) : (
                            <p className="text-sm mb-3 whitespace-pre-wrap">{response.message || response.content}</p>
                          )}

                          {/* Attachments within the message */}
                          {((response.attachments && response.attachments.length > 0) || response.attachment) && (
                            <div className="mt-3 pt-3 border-t border-gray-200">
                              <div className="text-xs text-gray-500 mb-2 flex items-center">
                                <Paperclip className="w-3 h-3 mr-1" />
                                {response.attachments && response.attachments.length > 1 ?
                                  `${response.attachments.length} Attachments` :
                                  'Attachment'
                                }
                              </div>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                {response.attachments && response.attachments.length > 0 ? (
                                  // Display multiple attachments
                                  response.attachments.map((attachment, index) => (
                                    <div key={index} className="flex items-center bg-white border rounded p-2">
                                      <FileText className="w-4 h-4 text-indigo-600 mr-2 flex-shrink-0" />
                                      <div className="flex-grow truncate">
                                        <div className="text-xs font-medium truncate">
                                          {attachment.file_name || attachment.file_url}
                                        </div>
                                        {attachment.file_size && (
                                          <div className="text-xs text-gray-400">
                                            {(attachment.file_size / 1024).toFixed(1)} KB
                                          </div>
                                        )}
                                      </div>
                                      <button
                                        onClick={() => downloadAttachment(attachment.id, attachment.file_name)}
                                        className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
                                        title="Download attachment"
                                      >
                                        <Download className="w-3 h-3" />
                                      </button>
                                    </div>
                                  ))
                                ) : (
                                  // Fallback to single attachment for backward compatibility
                                  <div className="flex items-center bg-white border rounded p-2">
                                    <FileText className="w-4 h-4 text-indigo-600 mr-2 flex-shrink-0" />
                                    <div className="flex-grow truncate">
                                      <div className="text-xs font-medium truncate">
                                        {response.attachment_name || response.attachment}
                                      </div>
                                    </div>
                                    {response.attachment_id ? (
                                      <button
                                        onClick={() => downloadAttachment(response.attachment_id, response.attachment_name)}
                                        className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
                                        title="Download attachment"
                                      >
                                        <Download className="w-3 h-3" />
                                      </button>
                                    ) : (
                                      <a
                                        href={response.attachment}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
                                        title="Download attachment"
                                      >
                                        <Download className="w-3 h-3" />
                                      </a>
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Reply and edit/delete buttons (for non-customer, non-internal messages) */}
                          <div className="mt-2 flex justify-end space-x-2">
                            {response.type === 'customer' && (
                              <button
                                className="text-xs text-indigo-700 flex items-center"
                                onClick={() => {
                                  // Focus the response box and optionally pre-fill with quoted text
                                  document.querySelector('textarea').focus();
                                }}
                              >
                                <Reply className="w-3 h-3 mr-1" />
                                Reply
                              </button>
                            )}

                            {/* Edit and Delete buttons for support messages that are not in edit mode */}
                            {response.type === 'zet' && !isEditingMessage && (
                              <>
                                <button
                                  className="text-xs text-gray-700 flex items-center"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEditMessage(response);
                                  }}
                                >
                                  <Edit className="w-3 h-3 mr-1" />
                                  Edit
                                </button>
                                <button
                                  className="text-xs text-red-700 flex items-center"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    openDeleteMessageModal(response);
                                  }}
                                >
                                  <Trash2 className="w-3 h-3 mr-1" />
                                  Delete
                                </button>
                              </>
                            )}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center text-gray-500 p-6 border rounded-md">
                        No messages available for this ticket
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Ticket Modal */}
      {isAddTicketModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[55] modal-backdrop">
          <div className="bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h2 className="text-xl font-bold text-gray-800">Create New Ticket</h2>
              <button
                onClick={closeAddTicketModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>
            <div className="p-6 space-y-4">
              {/* Form fields */}
              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">Subject*</label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  value={newTicket.subject}
                  onChange={handleNewTicketChange}
                  className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-200"
                  required
                />
              </div>

{/* Customer Search Field with Enhanced UI */}
<div className="space-y-2">
  <label htmlFor="customerSearch" className="block text-sm font-medium text-gray-700">
    Search Customer*
  </label>
  <div className="relative">
    <input
      type="text"
      id="customerSearch"
      value={userSearchQuery}
      onChange={handleUserSearchChange}
      onFocus={() => {
        if (searchResults.length > 0) setShowSearchResults(true);
      }}
      onBlur={() => {
        // Delay hiding so the click event on results can fire
        setTimeout(() => setShowSearchResults(false), 200);
      }}
      placeholder="Type at least 2 characters to search..."
      className="w-full p-2 pl-8 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-200"
    />
    <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />

    {isSearching && (
      <RefreshCw className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 animate-spin text-indigo-700" />
    )}

    {/* More visible search results */}
    {showSearchResults && (
      <div className="absolute z-10 w-full mt-1 bg-white border rounded-md shadow-lg max-h-60 overflow-auto">
        {searchResults.length > 0 ? (
          searchResults.map(user => (
            <div
              key={user.id}
              className="p-3 hover:bg-indigo-50 cursor-pointer border-b"
              onClick={() => handleUserSelect(user)}
            >
              <div className="font-medium text-indigo-700">{user.name || 'Unknown Name'}</div>
              <div className="text-xs text-gray-500">{user.email || 'No email'}</div>
            </div>
          ))
        ) : (
          userSearchQuery.length >= 2 && !isSearching ? (
            <div className="p-3 text-center text-gray-500">
              No users found matching "{userSearchQuery}"
            </div>
          ) : null
        )}
      </div>
    )}
  </div>

</div>

{/* Display Selected User */}
{selectedUser && (
  <div className="bg-indigo-50 p-3 rounded-md border border-indigo-100 flex justify-between items-center">
    <div>
      <div className="flex items-center">
        <User className="w-4 h-4 mr-2 text-indigo-600" />
        <span className="text-sm font-medium">{selectedUser.name}</span>
      </div>
      {selectedUser.email && (
        <div className="flex items-center mt-1">
          <Mail className="w-3 h-3 mr-2 text-gray-500" />
          <span className="text-xs text-gray-600">{selectedUser.email}</span>
        </div>
      )}
    </div>
    <div className="flex space-x-2">
      <button
        type="button"
        onClick={() => {
          setSelectedUser(null);
          setUserSearchQuery('');
          setNewTicket({
            ...newTicket,
            customer: '',
            customerEmail: '',
            customerId: ''
          });
        }}
        className="text-gray-500 hover:text-red-500"
      >
        <XCircle className="w-4 h-4" />
      </button>
    </div>
  </div>
)}



{/* Department dropdown using data from API */}
<div>
  <label htmlFor="department" className="block text-sm font-medium text-gray-700 mb-1">
    Department
  </label>
  <select
    id="department"
    name="department"
    value={newTicket.department}
    onChange={handleNewTicketChange}
    className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-200"
  >
    <option value="">Select Department</option>
    {departments.length > 0 ? (
      departments.map(dept => (
        <option key={dept.id} value={dept.name}>
          {dept.name}
        </option>
      ))
    ) : (
      <option value="Technical Support">Technical Support</option>
    )}
  </select>

                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                  <select
                    id="category"
                    name="category"
                    value={newTicket.category}
                    onChange={handleNewTicketChange}
                    className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-200"
                  >
                    <option value="Server Issue">Server Issue</option>
                    <option value="Network Issue">Network Issue</option>
                    <option value="Account Management">Account Management</option>
                    <option value="Maintenance">Maintenance</option>
                    <option value="Abuse">Abuse</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                  <select
                    id="priority"
                    name="priority"
                    value={newTicket.priority}
                    onChange={handleNewTicketChange}
                    className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-200"
                  >
                    <option value="Low">Low</option>
                    <option value="Medium">Medium</option>
                    <option value="High">High</option>
                    <option value="Critical">Critical</option>
                  </select>
                </div>
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">Description*</label>
                <textarea
                  id="description"
                  name="description"
                  value={newTicket.description}
                  onChange={handleNewTicketChange}
                  className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-200"
                  rows="5"
                  required
                ></textarea>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <button
                  onClick={closeAddTicketModal}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateTicket}
                  className="px-4 py-2 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Create Ticket
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Ticket Modal */}
      {isEditTicketModalOpen && editedTicket && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[56] modal-backdrop">
          <div className="bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h2 className="text-xl font-bold text-gray-800">Edit Ticket #{editedTicket.id}</h2>
              <button
                onClick={closeEditTicketModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>
            <div className="p-6 space-y-4">
              {/* Form fields */}
              <div>
                <label htmlFor="edit-subject" className="block text-sm font-medium text-gray-700 mb-1">Subject*</label>
                <input
                  type="text"
                  id="edit-subject"
                  name="subject"
                  value={editedTicket.subject}
                  onChange={handleEditedTicketChange}
                  className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-200"
                  required
                />
              </div>



              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
  <label htmlFor="edit-department" className="block text-sm font-medium text-gray-700 mb-1">Department</label>
  <select
    id="edit-department"
    name="department"
    value={editedTicket.department}
    onChange={handleEditedTicketChange}
    className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-200"
  >
    <option value="">Select Department</option>
    {departments.length > 0 ? (
      departments.map(dept => (
        <option key={dept.id} value={dept.name}>
          {dept.name}
        </option>
      ))
    ) : (
      <option value="Technical Support">Technical Support</option>
    )}
  </select>
</div>
                <div>
                  <label htmlFor="edit-category" className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                  <select
                    id="edit-category"
                    name="category"
                    value={editedTicket.category}
                    onChange={handleEditedTicketChange}
                    className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-200"
                  >
                    <option value="Server Issue">Server Issue</option>
                    <option value="Network Issue">Network Issue</option>
                    <option value="Account Management">Account Management</option>
                    <option value="Maintenance">Maintenance</option>
                    <option value="Abuse">Abuse</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="edit-priority" className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                  <select
                    id="edit-priority"
                    name="priority"
                    value={editedTicket.priority}
                    onChange={handleEditedTicketChange}
                    className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-200"
                  >
                    <option value="Low">Low</option>
                    <option value="Medium">Medium</option>
                    <option value="High">High</option>
                    <option value="Critical">Critical</option>
                  </select>
                </div>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <button
                  onClick={closeEditTicketModal}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleUpdateTicket}
                  className="px-4 py-2 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
                >
                  <Save className="w-4 h-4 mr-1" />
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reassign Ticket Modal */}
      {isReassignModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[57] modal-backdrop">
          <div className="bg-white rounded-lg shadow-lg max-w-md w-full overflow-hidden">
            <div className="p-6 border-b flex justify-between items-center">
              <h2 className="text-xl font-bold text-gray-800">Reassign Ticket</h2>
              <button
                onClick={closeReassignModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>
            <div className="p-6 space-y-4">
              <div>
                <label htmlFor="assignTo" className="block text-sm font-medium text-gray-700 mb-1">Assign To</label>
                <select
                  id="assignTo"
                  className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-200"
                  value={selectedAdmin}
                  onChange={handleAdminSelection}
                >
                  <option value="">Select Support Agent</option>
                  {adminList.map(admin => (
                    <option key={admin.id} value={admin.id}>{admin.name}</option>
                  ))}
                </select>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <button
                  onClick={closeReassignModal}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleReassignTicket}
                  className="px-4 py-2 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
                  disabled={!selectedAdmin}
                >
                  <Users className="w-4 h-4 mr-1" />
                  Reassign
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Message Confirmation Modal */}
      {isDeleteMessageModalOpen && messageToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[58] modal-backdrop">
          <div className="bg-white rounded-lg shadow-lg max-w-md w-full overflow-hidden">
            <div className="p-6 border-b flex justify-between items-center">
              <h2 className="text-xl font-bold text-gray-800">Delete Message</h2>
              <button
                onClick={closeDeleteMessageModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>
            <div className="p-6">
              <p className="mb-4">Are you sure you want to delete this message? This action cannot be undone.</p>
              <div className="p-3 bg-gray-50 border rounded-md mb-4">
                <p className="text-sm italic text-gray-700">{messageToDelete.message}</p>
              </div>
              <div className="flex justify-end gap-2">
                <button
                  onClick={closeDeleteMessageModal}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteMessage}
                  className="px-4 py-2 bg-red-600 text-white rounded-md flex items-center text-sm hover:bg-red-700"
                >
                  <Trash2 className="w-4 h-4 mr-1" />
                  Delete Message
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TicketsPage;